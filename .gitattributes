*.cs text eol=lf
*.cs text eol=lf
*.txt text eol=lf
*.txt text eol=lf
*.meta text eol=lf
*.meta text eol=lf
*.c text eol=lf
*.c text eol=lf
*.cpp text eol=lf
*.cpp text eol=lf
*.mm text eol=lf
*.mm text eol=lf
*.h text eol=lf
*.h text eol=lf
*.m text eol=lf
*.m text eol=lf
*.shader text eol=lf
*.shader text eol=lf
*.png filter=lfs diff=lfs merge=lfs -text
*.jpg filter=lfs diff=lfs merge=lfs -text
*.jpeg filter=lfs diff=lfs merge=lfs -text
*.mp4 filter=lfs diff=lfs merge=lfs -text
*.mp3 filter=lfs diff=lfs merge=lfs -text
*.wav filter=lfs diff=lfs merge=lfs -text
*.so filter=lfs diff=lfs merge=lfs -text
*.a filter=lfs diff=lfs merge=lfs -text
*.zip filter=lfs diff=lfs merge=lfs -text
*.ogg filter=lfs diff=lfs merge=lfs -text
*.exe filter=lfs diff=lfs merge=lfs -text
*.gz filter=lfs diff=lfs merge=lfs -text
*.pdf filter=lfs diff=lfs merge=lfs -text
*.aar filter=lfs diff=lfs merge=lfs -text
*.ide filter=lfs diff=lfs merge=lfs -text
*.jar filter=lfs diff=lfs merge=lfs -text
*.PNG filter=lfs diff=lfs merge=lfs -text
*.JPG filter=lfs diff=lfs merge=lfs -text
*.JPEG filter=lfs diff=lfs merge=lfs -text
*.sqlite filter=lfs diff=lfs merge=lfs -text
**/UniversalMediaPlayer.framework/UniversalMediaPlayer filter=lfs diff=lfs merge=lfs -text
**/FBSDKCoreKit.framework/FBSDKCoreKit filter=lfs diff=lfs merge=lfs -text
**/FBSDKShareKit.framework/FBSDKShareKit filter=lfs diff=lfs merge=lfs -text
**/Bolts.framework/Bolts filter=lfs diff=lfs merge=lfs -text
**/FBSDKLoginKit.framework/FBSDKLoginKit filter=lfs diff=lfs merge=lfs -text
**/LineAdapter.framework/Versions/A/LineAdapter filter=lfs diff=lfs merge=lfs -text
**/PlayServicesResolver/Editor/Google.JarResolver_*.dll filter=lfs diff=lfs merge=lfs -text
**/Smartlook.framework/Smartlook filter=lfs diff=lfs merge=lfs -text
*.unity3d filter=lfs diff=lfs merge=lfs -text
*.tgz filter=lfs diff=lfs merge=lfs -text
*.bundle filter=lfs diff=lfs merge=lfs -text
*.dll filter=lfs diff=lfs merge=lfs -text
*.meta text diff linguist-generated=false
