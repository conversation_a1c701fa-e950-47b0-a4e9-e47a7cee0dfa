using System;
using System.Collections;
using System.Collections.Generic;
using BBB.Core;
using BBB.Core.ResourcesManager;
using UnityEngine;
using Cysharp.Threading.Tasks;

namespace BBB.ContentManagement
{
    public class LoadSpriteFromAssetsResult : ISpriteResult
    {
        public string Uri { get; set; }
        public Sprite Sprite { get; set; }
        public string Tag { get; set; }
    }
    
    public class LoadSpriteFromAssetsTask : ContentTask
    {
        private readonly string _uri;
        private readonly string _tag;
        private readonly IAssetsManager _assetsManager;

        protected override object Identifier => $"{_uri}|{_tag}";

        public LoadSpriteFromAssetsTask(string uri, string tag, IAssetsManager assetsManager)
        {
            _uri = uri;
            _tag = tag;
            _assetsManager = assetsManager;
        }

        public override IEnumerator Tick()
        {
            if (IsAborted)
                yield break;

            var completed = false;
            
            _assetsManager
                .LoadSpriteAsync(_uri)
                .ContinueWith(sprite =>
                {
                    if (IsAborted)
                    {
                        completed = true;
                        return;
                    }

                    if (sprite != null)
                    {
                        var result = new LoadSpriteFromAssetsResult()
                        {
                            Sprite = sprite,
                            Tag = _tag,
                            Uri = _uri
                        };
                        
                        foreach (var receiver in ResultReceivers)
                        {
                            receiver.ReceiveResult(result);
                        }
                    }
                    else
                    {
                        BDebug.LogError(LogCat.Resources, $"Failed to load sprite '{_uri}' from assets.");
                    }

                    completed = true;
                });
            
            while (!completed && !IsAborted)
                yield return null;
            
        }
    }
}