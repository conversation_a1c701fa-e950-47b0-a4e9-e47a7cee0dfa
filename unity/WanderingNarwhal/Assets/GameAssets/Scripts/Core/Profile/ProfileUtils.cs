using System;
using System.Collections.Generic;
using GameAssets.Scripts.Player;
using UnityEngine;

namespace BebopBee.Social
{
    public static class ProfileUtils
    {
        public const string DefaultLevel = "level1";
        public const string DefaultLocation = MainProgressionLocation.UID;
        public const string DefaultCountry = "US";
        public const string DefaultFrameUid = "Default_AvatarFrame_Gold";
        public const string DefaultBadgeUid = "";
        public const string DefaultNameStyle = "Default";
        public static int GetConvertToInt(Dictionary<string, object> profileData, string key)
        {
            var value = profileData.Get(key, 0);
            if (value is string)
            {
                value = Convert.ToSingle(value);
            }

            return Convert.ToInt32(value);
        }

        public static double GetConvertToDouble(Dictionary<string, object> profileData, string key)
        {
            profileData.TryGetValue(key, out var value);
            return value == null ? 0.0 : Convert.ToDouble(value);
        }

        public static bool GetConvertToBool(Dictionary<string, object> profileData, string key)
        {
            var value = profileData.Get(key, false);
            
            if (value is not string stringValue) return Convert.ToBoolean(value);
            
            switch (stringValue)
            {
                case "1":
                case "True":
                case "true":
                    return true;
                case "0":
                case "False":
                case "false":
                    return false;
                default:
                    Debug.LogError($"Invalid string value {stringValue} of key {key}");
                    return false;
            }
        }
        
        public static bool IsStringValueNull(Dictionary<string, object> data, string key)
        {
            if (!data.ContainsKey(key)) return true;
            
            var value = GetConvertToString(data, key);

            return value.IsNullOrEmpty() || value.Equals("null") || value.Equals("Null") || value.Equals("None");
        }

        public static string GetConvertToString(Dictionary<string, object> profileData, string key)
        {
            return Convert.ToString(profileData.Get(key, string.Empty));
        }
    }
}