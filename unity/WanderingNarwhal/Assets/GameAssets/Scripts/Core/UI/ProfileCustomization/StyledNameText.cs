using BBB.DI;
using BBB.UI.Core;
using Febucci.UI;
using TMPro;
using UnityEngine;

namespace BBB.ProfileCustomization
{
    public class StyledNameText : ContextedUiBehaviour
    {

        [Header("Text Configuration")]
        [SerializeField] private TextMeshProUGUI _textComponent;
        [SerializeField] private TextAnimator_TMP _textAnimator;
        
        private INameStyleManager _nameStyleManager;
        private string _baseNameText;
        private string _currentStyledText;

        protected override void InitWithContextInternal(IContext context)
        {
            _nameStyleManager = context.Resolve<INameStyleManager>();
            UpdateStyledText();
        }
        
        public void SetText(string nameText)
        {
            _baseNameText = nameText;
            UpdateStyledText();
        }
        
        private void UpdateStyledText()
        {
            if (_nameStyleManager == null || string.IsNullOrEmpty(_baseNameText))
                return;

            var styledText = _nameStyleManager.ApplyCurrentNameStyle(_baseNameText);
            if (_currentStyledText == styledText)
            {
                return;
            }
            
            _currentStyledText = styledText;
                
            if (_textAnimator != null)
            {
                _textAnimator.SetText(styledText);
            }
            else if (_textComponent != null)
            {
                _textComponent.text = styledText;
            }
        }
    }
}
