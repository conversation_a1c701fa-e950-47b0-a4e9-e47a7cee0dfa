using System;

namespace BBB.ProfileCustomization
{
    public interface INameStyleManager
    {
        NameStyle CurrentNameStyle { get; }
        string GetStyleTag(NameStyle nameStyle);
        string ApplyNameStyle(string nameText, NameStyle nameStyle);
        string ApplyCurrentNameStyle(string nameText);
        void SetNameStyle(NameStyle nameStyle);
        bool IsNameStyleUnlocked(NameStyle nameStyle);
    }
}
