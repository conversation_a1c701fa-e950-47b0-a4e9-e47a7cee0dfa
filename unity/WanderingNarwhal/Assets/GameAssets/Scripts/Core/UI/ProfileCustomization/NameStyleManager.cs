using System;
using System.Collections.Generic;
using BBB.DI;
using BBB.ProfileCustomization.UI;
using BebopBee;
using UnityEngine;

namespace BBB.ProfileCustomization
{
    public class NameStyleManager : INameStyleManager, IContextInitializable, IContextReleasable
    {
        private readonly Dictionary<NameStyle, string> _styleTagMapping = new();
        private IAvatarDecorationManager _avatarDecorationManager;
        private IAccountManager _accountManager;
        private IInventory PlayerInventory => _playerManager.PlayerInventory;
        private IPlayerManager _playerManager;
        public NameStyle CurrentNameStyle { get; private set; } = NameStyle.Default;

        public void InitializeByContext(IContext context)
        {
            _avatarDecorationManager = context.Resolve<IAvatarDecorationManager>();
            _accountManager = context.Resolve<IAccountManager>();
            _playerManager = context.Resolve<IPlayerManager>();

            InitializeStyleTagMapping();
            LoadUserNameStyle();
        }

        public void ReleaseByContext(IContext context)
        {
            _styleTagMapping.Clear();
        }

        //YA: we can update default text style scriptable object tag keys to match NameStyle enum values if we decide to keep using text animator plugin
        private void InitializeStyleTagMapping()
        {
            _styleTagMapping.Clear();
            _styleTagMapping[NameStyle.Default] = "";
            _styleTagMapping[NameStyle.Rainbow] = "rainb";
            _styleTagMapping[NameStyle.Glow] = "glow";
            _styleTagMapping[NameStyle.Bounce] = "bounce";
            _styleTagMapping[NameStyle.Shake] = "shake";
            _styleTagMapping[NameStyle.Pulse] = "size";
            _styleTagMapping[NameStyle.Wave] = "wave";
            _styleTagMapping[NameStyle.Typewriter] = "typewriter";
            _styleTagMapping[NameStyle.Fade] = "fade";
            _styleTagMapping[NameStyle.Rotate] = "rot";
        }

        private void LoadUserNameStyle()
        {
            var profileNameStyle = _accountManager?.Profile?.NameStyle;
            if (!profileNameStyle.IsNullOrEmpty() && Enum.TryParse<NameStyle>(profileNameStyle, true, out var profileStyle))
            {
                CurrentNameStyle = profileStyle;
            }
        }
        
        public string GetStyleTag(NameStyle nameStyle)
        {
            return _styleTagMapping.GetValueOrDefault(nameStyle, string.Empty);
        }
        
        public string ApplyNameStyle(string nameText, NameStyle nameStyle)
        {
            if (nameText.IsNullOrEmpty() || nameStyle == NameStyle.Default)
            {
                return nameText;
            }

            var styleTag = GetStyleTag(nameStyle);
            return styleTag.IsNullOrEmpty() ? nameText : $"<{styleTag}>{nameText}</{styleTag}>";
        }
        
        public string ApplyCurrentNameStyle(string nameText)
        {
            return ApplyNameStyle(nameText, CurrentNameStyle);
        }
        
        public void SetNameStyle(NameStyle nameStyle)
        {
            if (CurrentNameStyle == nameStyle)
            {
                return;
            }
                
            CurrentNameStyle = nameStyle;
            SaveNameStyleToProfile();
        }
        
        private void SaveNameStyleToProfile()
        {
            if (_accountManager?.Profile != null)
            {
                _accountManager.Profile.NameStyle = CurrentNameStyle.ToString();
            }
        }
        
        public bool IsNameStyleUnlocked(NameStyle nameStyle)
        {
            if (nameStyle == NameStyle.Default)
            {
                return true;
            }

            if (_avatarDecorationManager?.NameStylesList == null)
            {
                return false;
            }
            
            var styleTag = GetStyleTag(nameStyle);
            return PlayerInventory.HasItem(styleTag);
        }
    }
}
