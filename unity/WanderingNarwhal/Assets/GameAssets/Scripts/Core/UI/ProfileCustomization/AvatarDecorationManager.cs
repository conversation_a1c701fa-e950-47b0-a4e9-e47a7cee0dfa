using System;
using System.Collections.Generic;
using BBB.DI;
using Core.Configs;
using FBConfig;
using UnityEngine;

namespace BBB.ProfileCustomization.UI
{
    public sealed class AvatarDecorationManager : IAvatarDecorationManager, IContextInitializable, IContextReleasable
    {
        private const string FrameLayerUid = "Frame";
        private const string BadgeLayerUid = "Badge";
        private const string NameStyleUid = "NameStyle";
        private static readonly Type[] RequiredConfigs =
        {
            typeof(ProfileCustomizationConfig)
        };

        private IInventory PlayerInventory => _playerManager.PlayerInventory;
        private IPlayerManager _playerManager;
        private IDictionary<string, ProfileCustomizationConfig> _profileCustomizationConfig;
        public List<ProfileCustomizationConfigItem> FramesList { get; private set; }
        public List<ProfileCustomizationConfigItem> BadgesList { get; private set; }
        public List<ProfileCustomizationConfigItem> NameStylesList { get; private set; }

        public void InitializeByContext(IContext context)
        {
            Config.OnConfigUpdated -= OnConfigUpdated;
            Config.OnConfigUpdated += OnConfigUpdated;
            _playerManager = context.Resolve<IPlayerManager>();
            FramesList = new List<ProfileCustomizationConfigItem>();
            BadgesList = new List<ProfileCustomizationConfigItem>();
            NameStylesList = new List<ProfileCustomizationConfigItem>();
        }

        private void OnConfigUpdated(IConfig config, HashSet<Type> updatedConfigs = null)
        {
            if (updatedConfigs != null && !updatedConfigs.Overlaps(RequiredConfigs))
                return;

            _profileCustomizationConfig = config.Get<ProfileCustomizationConfig>();
            
            if(_profileCustomizationConfig == null) return;
            FramesList.Clear();
            BadgesList.Clear();
            NameStylesList.Clear();

            LoadCustomizationList(FrameLayerUid, FramesList);
            LoadCustomizationList(BadgeLayerUid, BadgesList);
            LoadCustomizationList(NameStyleUid, NameStylesList);
        }

        private void LoadCustomizationList(string configUid, List<ProfileCustomizationConfigItem> targetList)
        {
            if (_profileCustomizationConfig.TryGetValue(configUid, out var config))
            {
                for (var i = 0; i < config.CustomizationsListLength; i++)
                {
                    var customization = config.CustomizationsList(i);
                    if (customization == null) continue;
                    targetList.Add(customization.Value);
                }
            }
        }

        public void AddGiftToPlayerInventory(string uid)
        {
            if (!CheckIfRewardExists(uid) || PlayerInventory.HasItem(uid)) return;
            PlayerInventory.AddItem(uid, 1);
        }

        private bool CheckIfRewardExists(string uid)
        {
             return _profileCustomizationConfig.ContainsKey(uid);
        }
        
        public void ReleaseByContext(IContext context)
        {
            Config.OnConfigUpdated -= OnConfigUpdated;
        }
    }
}