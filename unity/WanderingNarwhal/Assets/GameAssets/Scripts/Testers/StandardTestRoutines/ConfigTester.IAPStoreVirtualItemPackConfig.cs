using System;
using System.Collections;
using BBB.Store;
using FBConfig;
using UnityEngine;

namespace BBB.Testers
{
    public partial class ConfigTesterBehaviour
    {
        private IEnumerator TestIAPStoreVirtualItemPackConfig()
        {
            const string configName = "IAPStoreVirtualItemPackConfig";
            Debug.LogWarningFormat("Checking {0}", configName);
            var iapStoreVirtualItemPackConfig = _config.Get<IAPStoreVirtualItemPackConfig>();
            var index = 0f;
            foreach (var entry in iapStoreVirtualItemPackConfig.Values)
            {
                var uid = entry.Uid;

                //Int type is used for this enum
                if (!Enum.IsDefined(typeof(StorePackType), entry.Type))
                {
                    LogError("StorePackType is not defined for int {0} on {1}. The enum is int defined for IAPStoreVirtualItemPackConfig", entry.Type, uid);
                }
                
                if (entry.ItemUid.IsNullOrEmpty())
                {
                    LogError("ItemUid is not set for IAPStoreVirtualItemPackConfig {0}", uid);
                }
                
                // Debug.LogWarningFormat("Checking Name localization for IAPStoreVirtualItemPackConfig {0}", uid);
                _localization.getLocalizedText(entry.Name);
                
                if (entry.Icon.IsNullOrEmpty())
                {
                    LogError("Icon is not set for IAPStoreVirtualItemPackConfig {0}", uid);
                }
                
                // Debug.LogWarningFormat("Checking Description localization for IAPStoreVirtualItemPackConfig {0}", uid);
                _localization.getLocalizedText(entry.Description);
                
                if (entry.PacksFbLength == 0)
                {
                    LogError("Packs are not set for IAPStoreVirtualItemPackConfig {0}", uid);
                }
                else
                {
                    for (var i = 0; i < entry.PacksFbLength; i++)
                    {
                        var iapStoreVirtualItemPackDescription = entry.PacksFb(i);
                        
                        if (!iapStoreVirtualItemPackDescription.HasValue)
                        {
                            LogError("Pack item is not set for Packs in IAPStoreVirtualItemPackConfig {0}", uid);
                        }
                        else if (!iapStoreVirtualItemPackDescription.Value.Price.HasValue)
                        {
                            LogError("Price is not set for Packs in IAPStoreVirtualItemPackConfig {0}", uid);
                        }
                        else if (iapStoreVirtualItemPackDescription.Value.Price.Value.CurrenciesLength == 0)
                        {
                            LogError("Currencies are not set for Packs Price in IAPStoreVirtualItemPackConfig {0}", uid);
                        }
                    }
                }

                _progressCallback.SafeInvoke(index / iapStoreVirtualItemPackConfig.Values.Count, configName);
                index++;
                yield return null;
            }
        }
    }
}