using System.Collections;
using System.Linq;
using System.Text.RegularExpressions;
using Core.Configs;
using FBConfig;
using UnityEngine;

namespace BBB.Testers
{
    public partial class ConfigTesterBehaviour
    {
        private void ValidateLevelsNames()
        {
            var levelConfigs = _config.Get<ProgressionLevelConfig>();

            foreach (var levelConfig in levelConfigs.Values)
            {
                var digits = Regex.Match(levelConfig.Uid, @"\d+").Value;

                var parsedUid = int.Parse(digits);
                var parsedName = int.Parse(levelConfig.Name);

                if (parsedUid > 100)
                {
                    var expectedUid = levelConfig.LocationUid + "_tc" + parsedUid;
                    if (levelConfig.Uid != expectedUid)
                    {
                        LogError("Typo in VIP level {0}, expected {1}", levelConfig.Uid, expectedUid);
                    }

                    if (parsedName > 100)
                    {
                        LogError("VIP level uid {0} with name {1} should have name of previous actual level", levelConfig.Uid, levelConfig.Name);
                    }
                }
                else
                {
                    var expectedUid = levelConfig.LocationUid + parsedUid;
                    if (levelConfig.Uid != expectedUid)
                    {
                        LogError("Typo in level {0}, expected {1}", levelConfig.Uid, expectedUid);
                    }

                    if (digits != levelConfig.Name)
                    {
                        LogError("Mismatch of level uid {0} and its name {1}", levelConfig.Uid, levelConfig.Name);
                    }
                }
            }
        }

        private void ValidateSortOrder()
        {
            var levelConfigs = _config.Get<ProgressionLevelConfig>();
            var levelConfigsList = levelConfigs.Values.ToList();

            var sortOrders = levelConfigs.Values.OrderBy(x => x.SortOrder).Select(x => x.SortOrder).ToList();
            var distinctSortOrder = sortOrders.Distinct().ToList();

            // Check for duplicated sort order
            foreach (var sortOrder in distinctSortOrder)
            {
                var numberOfSortOrders = sortOrders.Count(x => x == sortOrder);

                if (numberOfSortOrders > 1)
                    LogError($"There are multiple levels ({numberOfSortOrders}) with the same sort order: {sortOrder}. Update LevelConfig! Levels:" +
                             string.Join(", ", levelConfigs.Values.Where(s => s.SortOrder == sortOrder).Select(lvl => lvl.Uid).ToArray()));
            }

            // Check for tc level sort order
            foreach (var levelConfig in levelConfigs.Values)
            {
                if (levelConfig.Uid.Contains("tc"))
                {
                    var nameOrder = levelConfig.Name;
                    var previousLevelConfig = levelConfigsList.Find(x => x.Name == nameOrder && x.LocationUid == levelConfig.LocationUid && !x.Uid.Contains("tc"));

                    if (previousLevelConfig.Equals(FlatBufferHelper.DefaultProgressionLevelConfig))
                    {
                        LogError("VIP level uid {0} with name {1} should have name of previous actual level", levelConfig.Uid, levelConfig.Name);
                        continue;
                    }

                    var sortOrder = previousLevelConfig.SortOrder;
                    var expectedSortOrder = sortOrder + 0.5f;

                    if (Mathf.RoundToInt(levelConfig.SortOrder * 10) != Mathf.RoundToInt(expectedSortOrder * 10))
                    {
                        LogError("VIP level uid {0} with sort order {1} expected to have sort order {2} based on previous level {3} with sort order {4}",
                            levelConfig.Uid, levelConfig.SortOrder, expectedSortOrder, previousLevelConfig.Uid, previousLevelConfig.SortOrder);
                        continue;
                    }
                }
            }
        }

        private IEnumerator TestLevelConfig()
        {
            var levelConfig = _config.Get<ProgressionLevelConfig>();
            var boosterConfig = _config.Get<BoosterConfig>();

            var configName = "LevelConfig";
            Debug.LogWarningFormat("Checking {0}", configName);

            ValidateLevelsNames();
            ValidateSortOrder();

            float index = 0f;
            foreach (var levelConfigEntry in levelConfig.Values)
            {
                if (levelConfigEntry.StartupBoostsLength > 0)
                {
                    for (var i = 0; i < levelConfigEntry.StartupBoostsLength; i++)
                    {
                        var boostUid = levelConfigEntry.StartupBoosts(i);
                        if (!boosterConfig.ContainsKey(boostUid))
                        {
                            LogError("Booster {0} not found in booster config", boostUid);
                        }
                    }
                }

                if (levelConfigEntry.EligibleBoostsLength > 0)
                {
                    for (var i = 0; i < levelConfigEntry.EligibleBoostsLength; i++)
                    {
                        var boostUid = levelConfigEntry.EligibleBoosts(i);
                        if (!boosterConfig.ContainsKey(boostUid))
                        {
                            LogError("Booster {0} not found in booster config", boostUid);
                        }
                    }
                }

                _progressCallback.SafeInvoke(index / levelConfig.Values.Count, configName);
                index++;
                yield return null;
            }
        }
    }
}