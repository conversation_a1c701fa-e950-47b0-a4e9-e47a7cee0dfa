using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using BBB.Quests;
using UnityEngine;
using LevelNarrativeConfig = FBConfig.LevelNarrativeConfig;

namespace BBB.Testers
{
    public partial class ConfigTesterBehaviour
    {
        private readonly List<string> Exceptions = new()
        {
            "elder_achievement",
            "travel_badge",
        };
    }

    public partial class ConfigTesterBehaviour
    {
        private IEnumerator TestQuestConfig()
        {
            Debug.LogWarning("Checking TestQuestConfig");
            var questConfigs = _config.Get<FBConfig.QuestConfig>();
            var index = 0f;
            var configName = "QuestConfig";

            foreach (var entry in questConfigs.Values)
            {
                var uid = entry.Uid;

                //Enum is string defined
                if (!Enum.IsDefined(typeof(QuestCategory), entry.Category))
                {
                    LogError("Category is not defined for string {0} on {1}. The enum is string defined for QuestConfig",
                        entry.Category, uid);
                }

                if (entry.Category != "LP" && entry.CharacterIcon.IsNullOrEmpty())
                {
                    LogError("CharacterIcon is not set for QuestConfig {0}", uid);
                }

                if (entry.Location.IsNullOrEmpty())
                {
                    if (entry.Category != "LP" && !Exceptions.Contains(entry.Uid))
                        LogError("Location is not set for QuestConfig {0}", uid);
                }

                if (entry.Priority < 0)
                {
                    LogError("Priority for {0} is below 0. Priority can't be negative in QuestConfig", uid);
                }

                var priorityConflictList = questConfigs.Values.Where(x =>
                    x.Location == entry.Location && x.Category == entry.Category && x.Priority == entry.Priority &&
                    x.Uid != entry.Uid);

                if (priorityConflictList.Count() > 0)
                {
                    LogError("Priority for {0} is in conflict with {1}", uid, priorityConflictList.First().Uid);
                }


                if (entry.Category == "WC")
                {
                }
                else
                {
                    if (entry.RequirementsLength == 0)
                    {
                        if (entry.Category == "WL")
                        {
                            var questsOnTheSameLocation =
                                questConfigs.Values.Where(x => x.Category == "WL" && x.Location == entry.Location);
                            var firstQuest = questsOnTheSameLocation.MinBy(x => x.Priority);

                            if (firstQuest.Uid != entry.Uid)
                                LogError("Requirements are not set for QuestConfig {0}", uid);
                        }
                        else if (entry.Category != "LP")
                        {
                            LogError("Requirements are not set for QuestConfig {0}", uid);
                        }
                    }
                    else
                    {
                        for (var i = 0; i < entry.RequirementsLength; i++)
                        {
                            var x = entry.Requirements(i);
                            if (!questConfigs.ContainsKey(x))
                            {
                                LogError(
                                    "RequirementUid {0} of QuestConfig {1} is not found in QuestConfig",
                                    x, uid);
                            }
                        }
                    }
                }

                if (entry.EnableCondition.IsNullOrEmpty())
                {
                    if (entry.Category == "WL")
                    {
                        var questsOnTheSameLocation =
                            questConfigs.Values.Where(x => x.Category == "WL" && x.Location == entry.Location);
                        var firstQuest = questsOnTheSameLocation.MinBy(x => x.Priority);

                        if (firstQuest.Uid != entry.Uid)
                            LogError("EnableCondition is not set for QuestConfig {0}", uid);
                    }
                    else
                    {
                        LogError("EnableCondition is not set for QuestConfig {0}", uid);
                    }
                }
                else
                {
                    ValidateLua(entry.EnableCondition, "For QuestConfig {0} EnableCondition {1}",
                        uid, entry.EnableCondition);
                }

                if (entry.Category != "LP" && !Exceptions.Contains(entry.Uid) && entry.RewardFbLength == 0)
                {
                    LogError("Reward is not set for QuestConfig {0}", uid);
                }

                if (!entry.CharacterText.IsNullOrEmpty() && !_localization.ContainsLocalizableText(entry.CharacterText))
                {
                    LogError("Character Text {0} for QuestConfig {1} is not localized", entry.CharacterText, uid);
                }

                if (!entry.QuestSubtitle.IsNullOrEmpty() && !_localization.ContainsLocalizableText(entry.QuestSubtitle))
                {
                    LogError("Quest Subtitle {0} for QuestConfig {1} is not localized", entry.CharacterText, uid);
                }

                if (!Exceptions.Contains(entry.Uid))
                {
                    if (entry.ObjectivesFbLength == 0)
                    {
                        LogError("Objectives are not set for QuestConfig {0}", uid);
                    }
                    else
                    {
                        // unified objectives test
                        for (var i = 0; i < entry.ObjectivesFbLength; i++)
                        {
                            var objective = entry.ObjectivesFb(i).Value;
                            if (objective.QuestId != entry.Uid)
                            {
                                LogError("Objective {0} has incorrect QuestId {1}, it should be {2}",
                                    objective.Uid, objective.QuestId, entry.Uid);
                            }

                            if (!objective.Parent.IsNullOrEmpty())
                            {
                                var counter = 0;
                                for (int j = 0; j < entry.ObjectivesFbLength; j++)
                                {
                                    var x = entry.ObjectivesFb(i).Value;
                                    if (x.Uid == objective.Parent)
                                    {
                                        counter++;
                                    }
                                }

                                if (counter != 1)
                                {
                                    LogError("Objective {0} has incorrect Parent {1}", objective.Uid,
                                        objective.Parent);
                                }
                            }

                            if (!objective.CompleteDesc.IsNullOrEmpty() && entry.Category != "LP")
                            {
                                LogError("Objective {0} has non-empty CompleteDesc. It is not used anymore, should be cleaned up",
                                    objective.Uid);
                            }

                            if (objective.Thumbnail.IsNullOrEmpty())
                            {
                                LogError("Objective {0} has not thumbnail", objective.Uid);
                            }

                            if (!objective.MessageFilter.IsNullOrEmpty())
                            {
                                LogError("Objective {0} has MessageFilter parameter, messaging isn't tested and shouldn't be used now",
                                    objective.Uid);
                            }

                            if (objective.CompleteCondition.IsNullOrEmpty())
                            {
                                LogError("Objective {0} has not complete condition", objective.Uid);
                            }
                            else
                            {
                                ValidateLua(objective.CompleteCondition,
                                    "For Objective {0} CompleteCondition {1}", objective.Uid,
                                    objective.CompleteCondition);
                            }

                            if (!objective.ProgressCondition.IsNullOrEmpty())
                            {
                                if (objective.StartProgress == objective.ProgressiveObjective)
                                {
                                    LogError("Objective {0} has the same StartProgress and ProgressiveObjective",
                                        objective.Uid);
                                }
                            }
                            else
                            {
                                if (objective.StartProgress != 0 || objective.ProgressiveObjective != 0)
                                {
                                    LogError("Objective {0} has not ProgressCondition, but Progress number non-zero",
                                        objective.Uid);
                                }
                            }

                            if (objective.RewardLength == 0)
                            {
                                LogError("Objective {0} has not reward", objective.Uid);
                            }

                            if (objective.CarRewardsLength > 0)
                            {
                                LogError("Objective {0} has CarRewards configured, but we don't use them anymore. Should be cleaned up",
                                    objective.Uid);
                            }

                            if (objective.PeopleRewardsLength > 0)
                            {
                                LogError("Objective {0} has PeopleRewards configured, but we don't use them anymore. Should be cleaned up",
                                    objective.Uid);
                            }

                            if (objective.DecorationRewardsLength > 0)
                            {
                                LogError("Objective {0} has DecorationRewards configured, but we don't use them anymore. Should be cleaned up",
                                    objective.Uid);
                            }

                            if (objective.ObjectiveCategory.IsNullOrEmpty())
                            {
                                LogError("Objective {0} has not ObjectiveCategory", objective.Uid);
                            }

                            if (objective.ObjectiveCategory != "Building" && entry.Category != "LP")
                            {
                                if (objective.TakeMeUid.IsNullOrEmpty())
                                {
                                    LogError("Objective {0} has not take me uid parameter",
                                        objective.Uid);
                                }
                                else
                                {
                                    if (objective.TakeMeParam.IsNullOrEmpty() && objective.TakeMeParamsLength == 0)
                                    {
                                        LogError(
                                            "Objective {0} has not any type of parameters for {1}",
                                            objective.Uid, objective.TakeMeUid);
                                    }
                                }
                            }
                        }

                        // specific for category objectives test

                        if (entry.ObjectivesFbLength == 1)
                        {
                            var objective = entry.ObjectivesFb(0).Value;

                            if (objective.ObjectiveCategory is "Level" or "Vip" or "Special")
                            {
                                if (objective.ObjectiveCategory == "Level" && !objective.ObjectiveCategoryParameter.IsNullOrEmpty())
                                {
                                    LogError("Objective {0} has not empty ObjectiveCategoryParameter. We don't need to configure it for Levels",
                                        objective.Uid);
                                }
                            }
                            else
                            {
                                LogError("Objective {0} has incorrect ObjectiveCategory",
                                    objective.Uid);
                            }
                        }
                        else if (entry.ObjectivesFbLength == 2)
                        {
                            var objective1 = entry.ObjectivesFb(0).Value;
                            var objective2 = entry.ObjectivesFb(1).Value;

                            if (objective1.ObjectiveCategory == "Blueprint" || objective2.ObjectiveCategory == "Building")
                            {
                                if (objective2.Parent == objective1.Uid)
                                {
                                    if (objective1.ObjectiveCategoryParameter.IsNullOrEmpty())
                                    {
                                        LogError("Objective {0} has not ObjectiveCategoryParameter. It should be POIEntityConfig based",
                                            objective1.Uid);
                                    }

                                    if (objective2.ObjectiveCategoryParameter.IsNullOrEmpty())
                                    {
                                        LogError("Objective {0} has not ObjectiveCategoryParameter. It should be MapPlaceableConfig based",
                                            objective2.Uid);
                                    }
                                }
                                else
                                {
                                    LogError("Objective {0} is not parented to {1}", objective2.Uid,
                                        objective1.Uid);
                                }
                            }
                            else
                            {
                                LogError("Objectives {0} and {1} have incorrect ObjectiveCategory. It should be a pair of Blueprint and then Building",
                                    objective1.Uid, objective2.Uid);
                            }
                        }
                        else
                        {
                            LogError("QuestConfig {0} has more than 2 objectives", uid);
                        }
                    }
                }

                _progressCallback.SafeInvoke(index / questConfigs.Values.Count, configName);
                index++;
                yield return null;
            }
        }
    }
}