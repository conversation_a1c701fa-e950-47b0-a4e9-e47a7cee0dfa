using System;
using BBB.DI;
using BBB.Navigation;
using BebopBee;
using GameAssets.Scripts.Collection;
using GameAssets.Scripts.Core.TimeManager;
using GameAssets.Scripts.Map;
using GameAssets.Scripts.SocialScreens.Teams;
using GameAssets.Scripts.SocialScreens.Teams.Screens.LogoSelectionScreen;
using GameAssets.Scripts.UI.OverlayDialog;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace BBB
{
    public class PassportTabInfoView : PassportTabView
    {
        public event Action BadgesClicked;
        public event Action ChangeNameClicked;

        private const string DateFormat = "PASSPORT_INFO_DATE_FORMAT";

        [SerializeField] private Button _badgesButton;
        [SerializeField] private Button _uidButton;
        [SerializeField] private NotifierWidget _badgesNotifier;

        [SerializeField] private LocalizedTextPro _levelText;
        [SerializeField] private TextMeshProUGUI _nameText;
        [SerializeField] private TextMeshProUGUI _date;
        [SerializeField] private YearBadgesView _yearBadgesView;
        [SerializeField] private TextMeshProUGUI _uid;
        [SerializeField] private GameObject _teamInfoContainer;
        [SerializeField] private TextMeshProUGUI _teamName;
        [SerializeField] private TeamLogoView _teamLogoView;
        [SerializeField] private OverlayDialogConfig _overlayDialogConfig;
        [SerializeField] private Transform _floatingTextAnchor;

        [SerializeField] private AsyncAvatar _asyncAvatar;
        [SerializeField] private Button[] _changeNameButtons;
        
        [SerializeField] private TextMeshProUGUI _firstTryWinsValueText;
        [SerializeField] private TextMeshProUGUI _helpsMadeValueText;
        [SerializeField] private TextMeshProUGUI _helpsReceivedValueText;
        [SerializeField] private TextMeshProUGUI _scenesCompletedValueText;
        [SerializeField] private TextMeshProUGUI _setsCompletedValueText;
        [SerializeField] private TextMeshProUGUI _leaguesWonValueText;

        private TimeManager _timeManager;
        private ILocalizationManager _localizationManager;
        private Profile _profile;
        private IPlayerManager _playerManager;
        private IAccountManager _accountManager;
        private ILevelsOrderingManager _levelsOrderingManager;
        private ISocialManager _socialManager;
        private ICollectionManager _collectionManager;
        private IEpisodeTaskManager _episodeTaskManager;
        private ILocationManager _locationManager;
        private GameNotificationManager _notificationManager;
        private IOverlayDialogManager _overlayDialogManager;

        private int _totalCityNumber;
        
        protected override void OnContextInitialized(IContext context)
        {
            _accountManager = context.Resolve<IAccountManager>();
            _profile = _accountManager.Profile;
            _timeManager = context.Resolve<TimeManager>();
            _localizationManager = context.Resolve<ILocalizationManager>();
            _playerManager = context.Resolve<IPlayerManager>();
            _levelsOrderingManager = context.Resolve<ILevelsOrderingManager>();
            _socialManager = context.Resolve<ISocialManager>();
            _collectionManager = context.Resolve<ICollectionManager>();
            _episodeTaskManager = context.Resolve<IEpisodeTaskManager>();
            _locationManager = context.Resolve<ILocationManager>();
            _notificationManager = context.Resolve<GameNotificationManager>();
            _badgesNotifier.Init(_notificationManager.GetBadgeQuestNotifier());
            _overlayDialogManager = context.Resolve<IOverlayDialogManager>();
            
            _badgesButton.ReplaceOnClick(OnBadgesClicked);
            _changeNameButtons.ReplaceOnClick(OnChangeNameClicked);
            _uidButton.ReplaceOnClick(OnUserIdClicked);
        }

        protected override void OnShow()
        {
            SetupPlayerInfo();
            SetupStats();
            
            CanBeFlipped = true;
        }

        private void SetupPlayerInfo()
        {
            SetupLevel();
            UpdatePlayerAvatar();
            _nameText.text = _accountManager.Profile.Name;
            SetupCurrentTeam();
            var date = Util.UtcTimeStampToLocalDateTime(_timeManager.InstallDate);
            _date.text = string.Format($"{{0:{_localizationManager.getLocalizedText(DateFormat)}}}", date);
            var daysSinceInstall = _timeManager.DaysSinceInstall + 1;
            _yearBadgesView.Refresh(daysSinceInstall);
            _uid.text = _profile.Uid;
        }

        private void SetupLevel()
        {
            var location = _locationManager.MainProgressionLocation;
            string levelName;
            if (location.IsCompleted)
            {
                var highestPassedLevelUid = location.GetHighestPassedLevelUid();
                levelName = _levelsOrderingManager.GetLevelName(highestPassedLevelUid);
            }
            else
            {
                var highestNonPassedLevelUid = location.GetLastUnlockedLevel();
                levelName = _levelsOrderingManager.GetLevelName(highestNonPassedLevelUid);
            }
            _levelText.FormatSetArgs(levelName);
        }
        
        private void SetupCurrentTeam()
        {
            var team = _socialManager.CurrentTeam;
            var showTeamInfo = team != null;
            _teamInfoContainer.SetActive(showTeamInfo);
            
            if (!showTeamInfo) return;
            
            _teamName.text = team.Name;
            _teamLogoView.Setup(team.Icon);
        }

        private void SetupStats()
        {
            _firstTryWinsValueText.text = _playerManager.Player.PlayerDO.Stats.FirstTryWinsCount.ToString();
            _helpsMadeValueText.text = _playerManager.Player.PlayerDO.Stats.TotalHelpsMadeCount.ToString();
            _helpsReceivedValueText.text = _playerManager.Player.PlayerDO.Stats.TotalHelpsReceivedCount.ToString();
            _scenesCompletedValueText.text = _episodeTaskManager.GetCompletedScenesCount().ToString();
            _setsCompletedValueText.text = _collectionManager.GetCompletedSetsCount().ToString();
            _leaguesWonValueText.text = _playerManager.Player.PlayerDO.Stats.LeaguesWon.ToString();
        }

        public override void PrepareForHideFlipping()
        {
        }

        protected override void OnHide()
        {
            CanBeFlipped = true;
        }

        private void OnBadgesClicked()
        {
            BadgesClicked?.Invoke();
        }

        private void OnChangeNameClicked()
        {
            ChangeNameClicked?.Invoke();
        }

        private void UpdatePlayerAvatar()
        {
            _asyncAvatar.Setup(new AvatarInfo(_accountManager.Profile.Avatar, _accountManager.Profile.Country));
        }
        
        private void OnUserIdClicked()
        {
            GUIUtility.systemCopyBuffer = _uid.text;
            
            _overlayDialogConfig.DisplayType = DisplayType.FloatingText;
            _overlayDialogConfig.TargetTransform = _floatingTextAnchor;
            _overlayDialogConfig.ShowBackground = false;
            _overlayDialogConfig.TextToDisplay = LocalizationManagerHelper.UserIdCopiedToClipboardKey;
            _overlayDialogManager.ShowOverlayDialog(_overlayDialogConfig);
        }
        
        public override void UserInteractionChanged(bool state)
        {
        }
    }
}