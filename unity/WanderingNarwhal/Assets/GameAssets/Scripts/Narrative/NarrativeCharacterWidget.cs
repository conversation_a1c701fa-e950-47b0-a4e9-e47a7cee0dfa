using System;
using System.Collections.Generic;
using BBB.Narrative.Views;
using Cysharp.Threading.Tasks;
using DG.Tweening;
using PBConfig;
using Spine.Unity;
using UnityEngine;

namespace BBB
{
    public enum NarrativeCharacterAnimationType
    {
        Tween = 0,
        Animator = 1
    }

    public enum SpeechBubbleType
    {
        LeftHandGeneric = 0,
        RightHandGeneric = 1
    }

    public enum SpeechBubbleColorPalette
    {
        Blue = 0,
        Red = 1
    }

    public class NarrativeCharacterWidget : BbbMonoBehaviour
    {
        [SerializeField] private Transform _messagePositionTarget;
        [SerializeField] private NarrativeCharacterAnimationType _animationType;
        [SerializeField] private Vector3 _hiddenPosition;
        [SerializeField] private Vector3 _shownPosition;
        [SerializeField] private float _showDuration = 1f;
        [SerializeField] private float _hideDuration = 1f;
        [SerializeField] private AnimationCurve _showAnimCurve;
        [SerializeField] private AnimationCurve _hideAnimCurve;
        [SerializeField] private Animator _animator;
        [SerializeField] private SkeletonGraphic _skeletonGraphic;
        [SerializeField] private SpeechBubbleType _speechBubbleType;
        [SerializeField] private SpeechBubbleColorPalette _speechBubbleColorPalette;

        private CharacterData _characterData;
        private Tween _tween;
        private bool _isShowing;
        private bool _isHiding;
        private UniTaskCompletionSource<bool> _showTcs;
        private UniTaskCompletionSource<bool> _hideTcs;
        private static readonly int Hide1 = Animator.StringToHash("Hide");
        private static readonly int Show1 = Animator.StringToHash("Show");

        public Vector3 MessagePosition => _messagePositionTarget.localPosition;

        public SpeechBubbleType SpeechBubbleType => _speechBubbleType;

        public SpeechBubbleColorPalette SpeechBubbleColorPalette => _speechBubbleColorPalette;

        public string Name => _characterData.Name;

        public void Setup(CharacterData characterData, Phrase phrase, Dictionary<string, object> specialParams)
        {
            _characterData = characterData;
            if (!_skeletonGraphic || phrase.AnimationName.IsNullOrEmpty()) return;

            if (_skeletonGraphic.SkeletonData.FindAnimation(phrase.AnimationName) != null)
            {
                _skeletonGraphic.AnimationState.SetAnimation(0, phrase.AnimationName, true);
            }
            else
            {
                Debug.LogError($"Couldn't fin animation {phrase.AnimationName} for character {characterData.Name}");
            }

            if (specialParams == null || !_skeletonGraphic || !specialParams.TryGetValue(LeagueExtensions.LeagueKey, out var leagueObj)) return;

            var league = (League) leagueObj;

            if (!_skeletonGraphic.IsValid)
            {
                _skeletonGraphic.Initialize(false);
            }

            _skeletonGraphic.Skeleton.SetSkin(league.ToSkinName());
        }

        public UniTask ShowAsync()
        {
            _showTcs = new UniTaskCompletionSource<bool>();

            switch (_animationType)
            {
                case NarrativeCharacterAnimationType.Tween:
                    transform.localPosition = _hiddenPosition;
                    _tween?.Complete();
                    _tween = transform
                        .DOLocalMove(_shownPosition, _showDuration)
                        .SetEase(_showAnimCurve)
                        .OnComplete(OnShowAnimationFinished);
                    break;

                case NarrativeCharacterAnimationType.Animator:
                    _animator.Rebind();
                    _animator.speed = 1f / _showDuration;
                    _animator.SetTrigger(Show1);
                    break;

                default:
                    throw new ArgumentOutOfRangeException();
            }

            _isShowing  = true;
            gameObject.SetActive(true);
            return _showTcs.Task;
        }

        public UniTask HideAsync()
        {
            _hideTcs = new UniTaskCompletionSource<bool>();

            switch (_animationType)
            {
                case NarrativeCharacterAnimationType.Tween:
                    _tween?.Complete();
                    _tween = transform
                        .DOLocalMove(_hiddenPosition, _hideDuration)
                        .SetEase(_hideAnimCurve)
                        .OnComplete(OnHideAnimationFinished);
                    break;

                case NarrativeCharacterAnimationType.Animator:
                    _animator.speed = 1f / _hideDuration;
                    _animator.SetTrigger(Hide1);
                    break;

                default:
                    throw new ArgumentOutOfRangeException();
            }

            _isHiding = true;
            return _hideTcs.Task;
        }

        private void OnShowAnimationFinished()
        {
            _tween      = null;
            _isShowing  = false;
            _showTcs?.TrySetResult(true);
            _showTcs = null;
        }

        private void OnHideAnimationFinished()
        {
            gameObject.SetActive(false);
            _tween     = null;
            _isHiding  = false;
            _hideTcs?.TrySetResult(true);
            _hideTcs = null;
        }

        public void Reset()
        {
            gameObject.SetActive(false);
            _animator?.Rebind();
            _tween?.Kill();
            _tween     = null;
            _showTcs   = null;
            _hideTcs   = null;
            _isShowing = false;
            _isHiding  = false;
            _characterData = null;
        }

        public void InstantShow()
        {
            if (_animationType == NarrativeCharacterAnimationType.Tween)
            {
                if (_isShowing && _tween != null)
                {
                    _tween.Complete();
                }
            }
            else
            {
                _animator.speed = 1f;
                _animator.ResetTrigger(Hide1);
                _animator.ResetTrigger(Show1);
                _animator.Play("Idle");
                OnShowAnimationFinished();
            }
        }

        public void InstantHide()
        {
            if (_animationType == NarrativeCharacterAnimationType.Tween)
            {
                if (_isHiding && _tween != null)
                {
                    _tween.Complete();
                }
            }
            else
            {
                _animator.speed = 1f;
                _animator.ResetTrigger(Hide1);
                _animator.ResetTrigger(Show1);
                _animator.Play("Start");
                OnHideAnimationFinished();
            }
        }
    }
}