using System;
using BBB.Core;
using BBB.Core.ResourcesManager;
using BBB.Navigation;
using BBB.ProfileCustomization;
using BBB.SocialScreens.Social.Ranks;
using BBB.UI.LoopScrollRect;
using Bebopbee.Core.Extensions.Unity;
using Cysharp.Threading.Tasks;
using TMPro;
using UnityEngine;
using UnityEngine.Profiling;
using UnityEngine.Serialization;
using UnityEngine.UI;

namespace BBB.Social.Ranks
{
    public class RankUiItem : LoopScrollListItem, IScrollable
    {
        private const string StatusTextEmptyLocKey = "SOCIAL_RANK_ITEM_STATIC_STATUS_LABEL";
        private const string YourNameLocKey = "YOU_STANDART";
        private const string ScoreThisWeekLoc = "SCORE_THIS_WEEK_LOC";
        private const string LevelNumber = "LEVEL_NUMBER";
        private const int PositionLimit = 99;
        private const string LevelUpToBanner = "LevelUpToBanner";

        private static readonly string[] RibbonNames =
        {
            "generic_ribbon_yellow",
            "generic_ribbon_purple",
            "generic_ribbon_yellow",
            "generic_ribbon_blue",
        };

        private static readonly string[] BoxNames =
        {
            "generic_box_red",
            "generic_box_blue",
            "generic_box_bronze",
            "generic_box_grey"
        };

        private static readonly string[] weeklyChestNames = 
        {
            "chest_gold",
            "chest_silver",
            "chest_bronze"
        };
        public event Action<string, Transform> RewardButtonClicked;

        [SerializeField] protected TextMeshProUGUI[] _rankTexts;
        [SerializeField] protected RankBadgePosition _badgePosition;

        [FormerlySerializedAs("_trophiesDebugText")] [SerializeField]
        private TextMeshProUGUI _trophiesText;

        [SerializeField] private GameObject[] _bgHolder;
        [SerializeField] private GameObject[] _localBGHolder;
        [SerializeField] private Image _weeklyIcon;
        [SerializeField] private Image _topPlayersIcon;

        [SerializeField] private GameObject _ranksAnimationFxPrefab;
        [SerializeField] private Transform _rewardPositionProvider;
        [SerializeField] private Button _rewardButton;
        [SerializeField] private GameObject _rewardHolder;
        [SerializeField] private Image _rewardBoxImage;
        [SerializeField] private Image _rewardRibbonImage;
        [SerializeField] private RankColorPalette _colorPalette;
        [SerializeField] private SetTouchableAnchors _setTouchableAnchors;

        [SerializeField] protected AsyncAvatar _asyncAvatar;
        [SerializeField] protected StyledNameText _nameText;

        [SerializeField] private int _currentPosition;
        [SerializeField] private Animator animator;

        private GameObject _ranksAnimationFxRoot;
        private string _currentUid;

        protected override void OnDisable()
        {
            _scalingRoot.localScale = Vector3.one;
            SetPlayRankProgressFx(false);
        }

        public void Init(LeaderboardManager.Score score,    
            ILocalizationManager localizationManager,
            ILevelsOrderingManager levelsOrderingManager,
            IAssetsManager assetsManager,
            string leaderboardCategory,
            WeeklyLeaderboardRewardsManager rewardsManager,
            LoopScrollSnappedItemVisibilitySwitcher loopScrollSnappedItemVisibilitySwitcher)
        {
            _nameText.SetText(score.IsOwnPlayer ? localizationManager.getLocalizedText(YourNameLocKey) : score.Name);

            if (_asyncAvatar != null)
            {
                _asyncAvatar.Setup(new AvatarInfo(score));
            }

            var isWeeklyTournament = leaderboardCategory == LeaderboardFilterTypes.PlayersWeekly;
            _weeklyIcon.enabled = isWeeklyTournament;
            _topPlayersIcon.enabled = !isWeeklyTournament;

            _bgHolder.Enable(!score.IsOwnPlayer);
            _localBGHolder.Enable(score.IsOwnPlayer);

            _rewardHolder.SetActive(false);
            _rewardButton.onClick.RemoveAllListeners();

            if (isWeeklyTournament)
            {
                foreach (var rankText in _rankTexts)
                {
                    rankText.text = localizationManager.getLocalizedText(ScoreThisWeekLoc);
                }

                var reward = rewardsManager.GetRewardFor(score.Position);

                if (reward != null)
                {
                    _rewardHolder.SetActive(true);
                    _rewardButton.ReplaceOnClick(RewardButtonClickHandler);
                    
                    var boxKey = PositionToBoxSpriteName(score.Position, leaderboardCategory);
                    assetsManager
                        .LoadSpriteAsync(boxKey)
                        .ContinueWith(sprite =>
                        {
                            if (sprite == null)
                            {
                                BDebug.LogError(LogCat.Resources, $"Couldn't load box sprite '{boxKey}'");
                            }
                            _rewardBoxImage.sprite = sprite;
                        });

                    var ribbonKey = PositionToRibbonSpriteName(score.Position, leaderboardCategory);
                    assetsManager
                        .LoadSpriteAsync(ribbonKey)
                        .ContinueWith(sprite =>
                        {
                            if (sprite == null)
                            {
                                BDebug.LogError(LogCat.Resources, $"Couldn't load ribbon sprite '{ribbonKey}'");
                            }
                            _rewardRibbonImage.sprite = sprite;
                        });
                    
                    var tintColor = PositionToBoxAndRibbonTintColor(score.Position, leaderboardCategory);
                    _rewardBoxImage.color = tintColor;
                    _rewardRibbonImage.color = tintColor;
                }
            }
            else
            {
                if (!score.LastLevel.IsNullOrEmpty())
                {
                    var levelName = score.LastLevel;
                    levelName = levelsOrderingManager.HasLevel(levelName) ? levelsOrderingManager.GetLevelName(levelName) : score.Trophies.ToString();

                    foreach (var rankText in _rankTexts)
                    {
                        rankText.text = localizationManager.getLocalizedTextWithArgs(LevelNumber, levelName);
                    }
                }
                else
                {
                    foreach (var rankText in _rankTexts)
                    {
                        rankText.text = localizationManager.getLocalizedText(StatusTextEmptyLocKey);
                    }
                }
            }

            _currentUid = score.Uid;
            _currentPosition = score.Position;
            _badgePosition.SetPosition(_currentPosition, _currentPosition >= PositionLimit, PositionLimit);

            var trophies = 0;
            if (score.Trophies >= 0)
            {
                trophies = score.Trophies;
            }
            else
            {
                BDebug.LogWarning(LogCat.Social, $"For {_currentUid} trying to render negative trophies {score.Trophies}");
            }

            _trophiesText.text = trophies.ToString();

            if (loopScrollSnappedItemVisibilitySwitcher != null
                && _setTouchableAnchors != null
                && loopScrollSnappedItemVisibilitySwitcher.TopSnappedItemInstance != null)
            {
                _setTouchableAnchors.SetAnchors(loopScrollSnappedItemVisibilitySwitcher.GetTopAnchor(), loopScrollSnappedItemVisibilitySwitcher.GetBottomAnchor());
            }
        }

        private Color PositionToBoxAndRibbonTintColor(int position, string leaderboardCategory)
        {
            var playerIndexOffset = LeaderboardViewPresenter.GetPlayerIndexOffsetForLeaderboard(leaderboardCategory);
            return position > 0 && position <= playerIndexOffset ? Color.white : _colorPalette.GetOutOfThreeTintColor();
        }

        private void RewardButtonClickHandler()
        {
            RewardButtonClicked?.Invoke(_currentUid, _rewardPositionProvider);
        }

        public void DecrementPositionInstant(int delta = 1)
        {
            _currentPosition -= delta;
            _badgePosition.SetPosition(_currentPosition);
        }

        public void IncrementPositionInstant(int delta = 1)
        {
            _currentPosition += delta;
            _badgePosition.SetPosition(_currentPosition);
        }

        public void UninitItem()
        {
            if (_asyncAvatar != null)
            {
                _asyncAvatar.ManualStop();
            }
        }

        public void SetPlayRankProgressFx(bool active)
        {
            if (active && _ranksAnimationFxRoot == null && _ranksAnimationFxPrefab != null)
            {
                Profiler.BeginSample($"Instantiate[{_ranksAnimationFxPrefab.name}]");
                _ranksAnimationFxRoot = Instantiate(_ranksAnimationFxPrefab, parent: transform);
                Profiler.EndSample();
                _ranksAnimationFxRoot.transform.SetAsLastSibling();
            }

            if (_ranksAnimationFxRoot != null)
            {
                _ranksAnimationFxRoot.SetActive(active);
            }
        }

        private static string PositionToRibbonSpriteName(int position, string leaderboardCategory)
        {
            var playerIndexOffset = LeaderboardViewPresenter.GetPlayerIndexOffsetForLeaderboard(leaderboardCategory);

            if (position > 0 && position <= playerIndexOffset)
                return string.Empty;

            return position > 0 && position <= RibbonNames.Length ? RibbonNames[position - 1] : RibbonNames[^1];
        }

        private static string PositionToBoxSpriteName(int position, string leaderboardCategory)
        {
            var playerIndexOffset = LeaderboardViewPresenter.GetPlayerIndexOffsetForLeaderboard(leaderboardCategory);

            if (position > 0 && position <= playerIndexOffset)
                return weeklyChestNames[Math.Min(position - 1, weeklyChestNames.Length - 1)];
            
            return position > 0 ? BoxNames[Math.Min(position - 1, BoxNames.Length - 1)] : BoxNames[^1];
        }

        public void PlayLevelUpAnimation()
        {
            if (animator != null)
            {
                animator.SetTrigger(LevelUpToBanner);
            }
        }

        #region IScrollable implementation

        public void InitItem(int itemId)
        {
        }

        #endregion
    }
}