using BBB.Core;
using BBB.DI;
using BebopBee;

namespace BBB.Quests
{
    public class ChangeNameModalController : BaseModalsController<IChangeNameViewPresenter>
    {
        private IAccountManager _accountManager;
        private bool _fromTutorial;
        
        public void SetupTutorialFlow()
        {
            _fromTutorial = true;
        }

        protected override void OnInitializeByContext(IContext context)
        {
            base.OnInitializeByContext(context);
            _accountManager = context.Resolve<IAccountManager>();
        }

        private void SaveNameAndAvatar()
        {
            _accountManager.ChangeNameAndAvatar(View.SelectedAvatar, View.SelectedDisplayName);

            // Save selected decorations
            if (!View.SelectedFrame.IsNullOrEmpty())
            {
                _accountManager.Profile.AvatarFrame = View.SelectedFrame;
            }

            if (!View.SelectedBadge.IsNullOrEmpty())
            {
                _accountManager.Profile.BadgeUid = View.SelectedBadge;
            }

            if (!View.SelectedNameStyle.IsNullOrEmpty())
            {
                _accountManager.Profile.NameStyle = View.SelectedNameStyle;
            }
        }

        protected override void OnShow()
        {
            base.OnShow();
            if (_fromTutorial)
            {
                View.EnableInputField();
            }
        }

        protected override void OnHide()
        {
            base.OnHide();
            SaveNameAndAvatar();
            _fromTutorial = false;
        }
    }
}