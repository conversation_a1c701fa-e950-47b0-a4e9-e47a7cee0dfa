using System;
using System.Collections;
using System.Collections.Generic;
using BBB.Core.Analytics;
using BBB.DI;
using BBB.Generic.Modal;
using BBB.Screens;
using BebopBee;
using GameAssets.Scripts.Core;
using GameAssets.Scripts.UI.OverlayDialog;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using BBB.ProfileCustomization;
using BBB.ProfileCustomization.UI;
using BebopBee.Social;

namespace BBB.Quests
{
    public class ChangeNameViewPresenter : ModalsViewPresenter, IChangeNameViewPresenter
    {
        [SerializeField] private int _minLength = 4;
        [SerializeField] private int _maxLength = 22;

        [Space]
        [SerializeField] private Button _changeNameButton;
        [SerializeField] private Button _doneButton;
        [SerializeField] private TMP_InputField _nameInputField;
        [SerializeField] private AsyncAvatar _asyncAvatar;
        [SerializeField] private TextMeshProUGUI _nameText;
        [SerializeField] private Color _defaultNameColor;
        [SerializeField] private Color _overridenNameColor;

        [SerializeField] private Transform _avatarsRoot;
        [SerializeField] private GameObject _avatarItemPrefab;
        [SerializeField] private GameObject _errorHandler;

        [SerializeField] private ScrollRect _scrollRect;
        [SerializeField] private OverlayDialogConfig _overlayDialogConfig;

        [Header("Tab System")]
        [SerializeField] private Button _avatarTabButton;
        [SerializeField] private Button _frameTabButton;
        [SerializeField] private Button _badgeTabButton;
        [SerializeField] private Button _nameTabButton;

        [SerializeField] private GameObject _avatarTabContent;
        [SerializeField] private GameObject _frameTabContent;
        [SerializeField] private GameObject _badgeTabContent;
        [SerializeField] private GameObject _nameTabContent;

        [Header("Decoration Items")]
        [SerializeField] private Transform _framesRoot;
        [SerializeField] private Transform _badgesRoot;
        [SerializeField] private GameObject _decorationItemPrefab;
        [SerializeField] private GameObject _lockedItemPrefab;

        private IAccountManager _accountManager;
        private IOverlayDialogManager _overlayDialogManager;
        private GenericModalFactory _genericModalFactory;
        private IModalsBuilder _modalsBuilder;
        private IAvatarDecorationManager _avatarDecorationManager;

        private readonly List<AvatarItem> _avatars = new();
        private readonly List<DecorationItem> _frames = new();
        private readonly List<DecorationItem> _badges = new();
        private AvatarItem _currentlySelectedAvatar;
        private DecorationItem _currentlySelectedFrame;
        private DecorationItem _currentlySelectedBadge;

        private AvatarCategories _currentTab = AvatarCategories.Avatar;

        public string SelectedDisplayName { get; private set; } = string.Empty;

        public string SelectedAvatar => _currentlySelectedAvatar != null ? _currentlySelectedAvatar.AvatarName : string.Empty;
        public string SelectedFrame => _currentlySelectedFrame != null ? _currentlySelectedFrame.DecorationUid : string.Empty;
        public string SelectedBadge => _currentlySelectedBadge != null ? _currentlySelectedBadge.DecorationUid : string.Empty;

        protected override void OnContextInitialized(IContext context)
        {
            base.OnContextInitialized(context);
            _accountManager = context.Resolve<IAccountManager>();
            _overlayDialogManager = context.Resolve<IOverlayDialogManager>();
            _genericModalFactory = context.Resolve<GenericModalFactory>();
            _modalsBuilder = context.Resolve<IModalsBuilder>();
            _avatarDecorationManager = context.Resolve<IAvatarDecorationManager>();

            _doneButton.ReplaceOnClick(DoneButtonClickedHandler);
            SetupTabButtons();
        }

        private bool IsCurrentNameValid()
        {
            var currentText = _nameInputField.text;
            return currentText.Length >= _minLength && currentText.Length <= _maxLength;
        }

        private void DoneButtonClickedHandler()
        {
            if (!IsCurrentNameValid()) return;

            if (!ConnectivityStatusManager.ConnectivityReachable)
            {
                _overlayDialogConfig.DisplayType = DisplayType.FloatingText;
                _overlayDialogConfig.TargetTransform = _doneButton.transform;
                _overlayDialogConfig.ShowBackground = true;
                _overlayDialogConfig.TextToDisplay = LocalizationManagerHelper.OfflineConnectionProblemKey;
                _overlayDialogManager.ShowOverlayDialog(_overlayDialogConfig);
                return;
            }
            
            TriggerOnCloseClicked();
        }

        protected override void OnShow()
        {
            base.OnShow();

            _errorHandler.SetActive(false);
            _scrollRect.verticalNormalizedPosition = 1f;
            _nameInputField.interactable = false;

            var displayName = _accountManager.Profile.DisplayName.Trim();
            if (displayName.Length > _maxLength)
            {
                displayName = displayName[.._maxLength];
            }

            RefreshNameColor();
            _nameInputField.text = displayName;
            SelectedDisplayName = displayName;
            SubmitProfileName(displayName);

            _changeNameButton.ReplaceOnClick(EnableInputField);
            _nameInputField.onEndEdit.RemoveAllListeners();
            _nameInputField.onEndEdit.AddListener(SubmitProfileName);

            RefreshAvatar();
            UpdateTabVisuals();
            SetupTabContent();
        }

        private void RefreshNameColor()
        {
            _nameText.color = _accountManager.Profile.IsDisplayNameOverriden ? _overridenNameColor : _defaultNameColor;
        }

        protected override void OnHide()
        {
            base.OnHide();
            _asyncAvatar.ManualStop();
        }

        public void EnableInputField()
        {
            if (!_nameInputField.interactable)
            {
                _errorHandler.SetActive(false);
                _nameInputField.interactable = true;
            }

            if (_nameInputField.isActiveAndEnabled)
            {
                _nameInputField.ActivateInputField();
            }

            _accountManager.Profile.IsDisplayNameOverriden = true;
            RefreshNameColor();
        }

        private void SubmitProfileName(string text)
        {
            var newDisplayName = text;
            newDisplayName = newDisplayName.Trim();

            if (!newDisplayName.IsNullOrEmpty())
            {
                if (newDisplayName.Length < _minLength)
                {
                    _errorHandler.SetActive(true);
                }
                else if (newDisplayName.Length > _maxLength)
                {
                    _errorHandler.SetActive(true);
                }
                else if (newDisplayName != SelectedDisplayName)
                {
                    _errorHandler.SetActive(false);
                    SelectedDisplayName = newDisplayName;
                    StartCoroutine(DelayedInputDisabler());
                }
            }
            else
            {
                _errorHandler.SetActive(true);
                _nameInputField.text = SelectedDisplayName;
                StartCoroutine(DelayedInputDisabler());
            }
        }

        private IEnumerator DelayedInputDisabler()
        {
            yield return null;
            _nameInputField.interactable = false;
        }

        private void FillAvatars()
        {
            _avatarsRoot.RemoveAllActiveChilden();
            _avatars.Clear();
            _currentlySelectedAvatar = null;

            var profile = _accountManager.Profile;
            var currentAvatar = profile.Avatar;

            foreach (var defaultAvatar in GenericResourceProvider.DefaultAvatars)
            {
                CreateAvatarItem(defaultAvatar, AvatarSelectedHandler);
            }
            
            var socialAvatarName = GetSocialAvatarName();
            if (!currentAvatar.IsNullOrEmpty() && !GenericResourceProvider.DefaultAvatars.Contains(currentAvatar) && socialAvatarName != currentAvatar)
            {
                CreateAvatarItem(currentAvatar, AvatarSelectedHandler);
            }

            if (!socialAvatarName.IsNullOrEmpty())
            {
                CreateAvatarItem(socialAvatarName, SocialAvatarSelectedHandler, true);
            }
            return;

            void CreateAvatarItem(string avatarName, Action<string> selectedCallback, bool placeFirst = false)
            {
                UnityEngine.Profiling.Profiler.BeginSample($"Instantiate[{_avatarItemPrefab.name}]");
                var go = Instantiate(_avatarItemPrefab, _avatarsRoot);
                UnityEngine.Profiling.Profiler.EndSample();
                go.SetActive(true);

                var avatarItem = go.GetComponent<AvatarItem>();
                _avatars.Add(avatarItem);
                
                avatarItem.Setup(avatarName, selectedCallback);
                var current = avatarName == currentAvatar;

                if (placeFirst || current)
                {
                    avatarItem.transform.SetAsFirstSibling();
                }
                
                if (!current) return;

                avatarItem.SetAsSelected(true);
                _currentlySelectedAvatar = avatarItem;
            }
        }

        private string GetSocialAvatarName()
        {
            //even if we have user logged in with different platforms having multiple social avatars - we're showing only 1 of them. The facebook one has the most priority.
            var profile = _accountManager.Profile;

            if (!profile.FacebookAvatar.IsNullOrEmpty())
            {
                return profile.FacebookAvatar;
            }
            
            if (!profile.GoogleAvatar.IsNullOrEmpty())
            {
                return profile.GoogleAvatar;
            }
            
            if (!profile.SignedAvatar.IsNullOrEmpty())
            {
                return profile.SignedAvatar;
            }

            return _accountManager.IsAnyPlatformLoggedIn ? string.Empty : GenericResourceProvider.FacebookAvatar;
        }

        private void SocialAvatarSelectedHandler(string avatarName)
        {
            if (avatarName.IsNullOrEmpty())
            {
                return;
            }
            if (avatarName != GenericResourceProvider.FacebookAvatar)
            {
                AvatarSelectedHandler(avatarName);
                return;
            }
            
            Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.EditProfile.CategoryName, DauInteractions.EditProfile.Login, string.Empty));

            if (!ConnectivityStatusManager.ConnectivityReachable)
            {
                _genericModalFactory.ShowNoConnectionModal();
                return;
            }

            var ctrl = _modalsBuilder.CreateModalView<SaveProgressModalController>(ModalsType.SaveProgress);
            ctrl.ShowModal();
        }

        private void AvatarSelectedHandler(string avatarName)
        {
            if (_currentlySelectedAvatar == null)
            {
                Debug.LogError("You should always have _currentlySelectedAvatar");
            }
            else
            {
                if (_currentlySelectedAvatar.AvatarName == avatarName)
                    return;

                _currentlySelectedAvatar.SetAsSelected(false);
            }

            var selected = _avatars.Find(x => x.AvatarName == avatarName);

            _currentlySelectedAvatar = selected;
            _currentlySelectedAvatar.SetAsSelected(true);
            RefreshAvatar(SelectedAvatar);
        }

        private void RefreshAvatar(string avatarName = null)
        {
            if (avatarName.IsNullOrEmpty())
            {
                avatarName = _accountManager.Profile.Avatar;
            }
            _asyncAvatar.ManualStop();
            _asyncAvatar.Setup(new AvatarInfo(avatarName, _accountManager.Profile.Country));
        }

        private void SetupTabButtons()
        {
            _avatarTabButton?.ReplaceOnClick(() => SwitchToTab(AvatarCategories.Avatar));
            _frameTabButton?.ReplaceOnClick(() => SwitchToTab(AvatarCategories.Frames));
            _badgeTabButton?.ReplaceOnClick(() => SwitchToTab(AvatarCategories.Badges));
            _nameTabButton?.ReplaceOnClick(() => SwitchToTab(AvatarCategories.Name));
        }

        private void SwitchToTab(AvatarCategories tab)
        {
            if (_currentTab == tab) return;

            _currentTab = tab;
            UpdateTabVisuals();
            SetupTabContent();
        }

        private void UpdateTabVisuals()
        {
            // Hide all tab contents
            _avatarTabContent?.SetActive(false);
            _frameTabContent?.SetActive(false);
            _badgeTabContent?.SetActive(false);
            _nameTabContent?.SetActive(false);

            // Show current tab content
            switch (_currentTab)
            {
                case AvatarCategories.Avatar:
                    _avatarTabContent?.SetActive(true);
                    break;
                case AvatarCategories.Frames:
                    _frameTabContent?.SetActive(true);
                    break;
                case AvatarCategories.Badges:
                    _badgeTabContent?.SetActive(true);
                    break;
                case AvatarCategories.Name:
                    _nameTabContent?.SetActive(true);
                    break;
            }
        }

        private void SetupTabContent()
        {
            switch (_currentTab)
            {
                case AvatarCategories.Avatar:
                    FillAvatars();
                    break;
                case AvatarCategories.Frames:
                    FillFrames();
                    break;
                case AvatarCategories.Badges:
                    FillBadges();
                    break;
                case AvatarCategories.Name:
                    // Name tab content is handled by existing name input field
                    break;
            }
        }

        private void FillFrames()
        {
            _framesRoot.RemoveAllActiveChilden();
            _frames.Clear();
            _currentlySelectedFrame = null;

            // Get available frames from avatar decoration manager
            // For now, using placeholder logic - this should be replaced with actual decoration manager calls
            var availableFrames = GetAvailableDecorations(AvatarCategories.Frames);
            var currentFrame = _accountManager.Profile.AvatarFrame ?? ProfileUtils.DefaultFrameUid;

            foreach (var frameData in availableFrames)
            {
                CreateDecorationItem(frameData, _framesRoot, FrameSelectedHandler, currentFrame);
            }
        }

        private void FillBadges()
        {
            _badgesRoot.RemoveAllActiveChilden();
            _badges.Clear();
            _currentlySelectedBadge = null;

            // Get available badges from avatar decoration manager
            var availableBadges = GetAvailableDecorations(AvatarCategories.Badges);
            var currentBadge = _accountManager.Profile.BadgeUid ?? ProfileUtils.DefaultBadgeUid;

            foreach (var badgeData in availableBadges)
            {
                CreateDecorationItem(badgeData, _badgesRoot, BadgeSelectedHandler, currentBadge);
            }
        }

        private List<DecorationData> GetAvailableDecorations(AvatarCategories category)
        {
            var decorations = new List<DecorationData>();

            switch (category)
            {
                case AvatarCategories.Frames:
                    // Add default frames from GenericResourceProvider
                    foreach (var frameUid in GenericResourceProvider.DefaultAvatarsFrames)
                    {
                        decorations.Add(new DecorationData { Uid = frameUid, IsUnlocked = true });
                    }

                    // Add some locked premium frames as examples
                    decorations.Add(new DecorationData { Uid = "Premium_AvatarFrame_Diamond", IsUnlocked = false });
                    decorations.Add(new DecorationData { Uid = "Premium_AvatarFrame_Platinum", IsUnlocked = false });
                    break;

                case AvatarCategories.Badges:
                    // Add default badge (empty)
                    if (!ProfileUtils.DefaultBadgeUid.IsNullOrEmpty())
                    {
                        decorations.Add(new DecorationData { Uid = ProfileUtils.DefaultBadgeUid, IsUnlocked = true });
                    }

                    // Add some example badges
                    decorations.Add(new DecorationData { Uid = "badge_champion", IsUnlocked = true });
                    decorations.Add(new DecorationData { Uid = "badge_master", IsUnlocked = false });
                    decorations.Add(new DecorationData { Uid = "badge_legend", IsUnlocked = false });
                    break;
            }

            return decorations;
        }

        private void CreateDecorationItem(DecorationData decorationData, Transform parent, Action<string> selectedCallback, string currentSelection)
        {
            var prefabToUse = decorationData.IsUnlocked ? _decorationItemPrefab : _lockedItemPrefab;
            if (prefabToUse == null) return;

            UnityEngine.Profiling.Profiler.BeginSample($"Instantiate[{prefabToUse.name}]");
            var go = Instantiate(prefabToUse, parent);
            UnityEngine.Profiling.Profiler.EndSample();
            go.SetActive(true);

            var decorationItem = go.GetComponent<DecorationItem>();
            if (decorationItem == null)
            {
                decorationItem = go.AddComponent<DecorationItem>();
            }

            decorationItem.Setup(decorationData.Uid, selectedCallback, decorationData.IsUnlocked);
            var isCurrent = decorationData.Uid == currentSelection;

            if (isCurrent)
            {
                decorationItem.SetAsSelected(true);
                if (parent == _framesRoot)
                {
                    _currentlySelectedFrame = decorationItem;
                    _frames.Add(decorationItem);
                }
                else if (parent == _badgesRoot)
                {
                    _currentlySelectedBadge = decorationItem;
                    _badges.Add(decorationItem);
                }
            }
            else
            {
                if (parent == _framesRoot)
                {
                    _frames.Add(decorationItem);
                }
                else if (parent == _badgesRoot)
                {
                    _badges.Add(decorationItem);
                }
            }
        }

        private void FrameSelectedHandler(string frameUid)
        {
            if (_currentlySelectedFrame != null && _currentlySelectedFrame.DecorationUid == frameUid)
                return;

            if (_currentlySelectedFrame != null)
            {
                _currentlySelectedFrame.SetAsSelected(false);
            }

            var selected = _frames.Find(x => x.DecorationUid == frameUid);
            _currentlySelectedFrame = selected;
            _currentlySelectedFrame?.SetAsSelected(true);

            // Update avatar preview with new frame
            RefreshAvatarWithDecorations();
        }

        private void BadgeSelectedHandler(string badgeUid)
        {
            if (_currentlySelectedBadge != null && _currentlySelectedBadge.DecorationUid == badgeUid)
                return;

            if (_currentlySelectedBadge != null)
            {
                _currentlySelectedBadge.SetAsSelected(false);
            }

            var selected = _badges.Find(x => x.DecorationUid == badgeUid);
            _currentlySelectedBadge = selected;
            _currentlySelectedBadge?.SetAsSelected(true);

            // Update avatar preview with new badge
            RefreshAvatarWithDecorations();
        }

        private void RefreshAvatarWithDecorations()
        {
            var avatarName = SelectedAvatar.IsNullOrEmpty() ? _accountManager.Profile.Avatar : SelectedAvatar;
            var frameUid = SelectedFrame.IsNullOrEmpty() ? ProfileUtils.DefaultFrameUid : SelectedFrame;
            var badgeUid = SelectedBadge.IsNullOrEmpty() ? ProfileUtils.DefaultBadgeUid : SelectedBadge;

            _asyncAvatar.ManualStop();
            _asyncAvatar.Setup(new AvatarInfo(avatarName, _accountManager.Profile.Country, frameUid, badgeUid));
        }
    }

    [System.Serializable]
    public class DecorationData
    {
        public string Uid;
        public bool IsUnlocked;
    }

    public class DecorationItem : MonoBehaviour
    {
        [SerializeField] private AsyncImage _decorationImage;
        [SerializeField] private GameObject _selectedHolder;
        [SerializeField] private GameObject _lockedOverlay;
        [SerializeField] private Button _button;

        public string DecorationUid { get; private set; }
        private Action<string> _selectedCallback;
        private bool _isUnlocked;

        private void Awake()
        {
            if (_button != null)
            {
                _button.onClick.AddListener(() =>
                {
                    if (_isUnlocked)
                    {
                        _selectedCallback?.Invoke(DecorationUid);
                    }
                });
            }
        }

        public void Setup(string decorationUid, Action<string> selectedCallback, bool isUnlocked)
        {
            DecorationUid = decorationUid;
            _selectedCallback = selectedCallback;
            _isUnlocked = isUnlocked;

            SetAsSelected(false);

            if (_lockedOverlay != null)
            {
                _lockedOverlay.SetActive(!isUnlocked);
            }

            // Load decoration sprite
            if (_decorationImage != null && !decorationUid.IsNullOrEmpty())
            {
                _decorationImage.Load(decorationUid, "decorations", error =>
                {
                    // Handle error loading decoration image
                });
            }
        }

        public void SetAsSelected(bool selected)
        {
            if (_selectedHolder != null)
            {
                _selectedHolder.SetActive(selected);
            }
        }
    }
}