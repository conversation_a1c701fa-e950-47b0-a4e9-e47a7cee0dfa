using System;
using System.Collections;
using System.Collections.Generic;
using BBB.Core.Analytics;
using BBB.DI;
using BBB.Generic.Modal;
using BBB.Screens;
using BebopBee;
using GameAssets.Scripts.Core;
using GameAssets.Scripts.UI.OverlayDialog;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using BBB.ProfileCustomization;
using BBB.ProfileCustomization.UI;
using BebopBee.Social;
using FBConfig;

namespace BBB.Quests
{
    public class ChangeNameViewPresenter : ModalsViewPresenter, IChangeNameViewPresenter
    {
        [SerializeField] private int _minLength = 4;
        [SerializeField] private int _maxLength = 22;

        [Space]
        [SerializeField] private Button _changeNameButton;
        [SerializeField] private Button _doneButton;
        [SerializeField] private TMP_InputField _nameInputField;
        [SerializeField] private AsyncAvatar _asyncAvatar;
        [SerializeField] private TextMeshProUGUI _nameText;
        [SerializeField] private Color _defaultNameColor;
        [SerializeField] private Color _overridenNameColor;

        [SerializeField] private Transform _avatarsRoot;
        [SerializeField] private GameObject _avatarItemPrefab;
        [SerializeField] private GameObject _errorHandler;

        [SerializeField] private ScrollRect _scrollRect;
        [SerializeField] private OverlayDialogConfig _overlayDialogConfig;

        [Header("Tab System")]
        [SerializeField] private Button _avatarTabButton;
        [SerializeField] private Button _frameTabButton;
        [SerializeField] private Button _badgeTabButton;
        [SerializeField] private Button _nameTabButton;

        [SerializeField] private GameObject _avatarTabContent;
        [SerializeField] private GameObject _frameTabContent;
        [SerializeField] private GameObject _badgeTabContent;
        [SerializeField] private GameObject _nameTabContent;

        [Header("Decoration Items")]
        [SerializeField] private GameObject _lockedItemPrefab;

        private IAccountManager _accountManager;
        private IOverlayDialogManager _overlayDialogManager;
        private GenericModalFactory _genericModalFactory;
        private IModalsBuilder _modalsBuilder;
        private IAvatarDecorationManager _avatarDecorationManager;
        private ILockManager _lockManager;

        private readonly List<AvatarItem> _avatars = new();
        private readonly List<AvatarItem> _decorationItems = new();
        private AvatarItem _currentlySelectedAvatar;
        private AvatarItem _currentlySelectedFrame;
        private AvatarItem _currentlySelectedBadge;

        private AvatarCategories _currentTab = AvatarCategories.Avatar;

        public string SelectedDisplayName { get; private set; } = string.Empty;

        public string SelectedAvatar => _currentlySelectedAvatar != null ? _currentlySelectedAvatar.ItemUid : string.Empty;
        public string SelectedFrame => _currentlySelectedFrame != null ? _currentlySelectedFrame.ItemUid : string.Empty;
        public string SelectedBadge => _currentlySelectedBadge != null ? _currentlySelectedBadge.ItemUid : string.Empty;

        protected override void OnContextInitialized(IContext context)
        {
            base.OnContextInitialized(context);
            _accountManager = context.Resolve<IAccountManager>();
            _overlayDialogManager = context.Resolve<IOverlayDialogManager>();
            _genericModalFactory = context.Resolve<GenericModalFactory>();
            _modalsBuilder = context.Resolve<IModalsBuilder>();
            _avatarDecorationManager = context.Resolve<IAvatarDecorationManager>();
            _lockManager = context.Resolve<ILockManager>();

            _doneButton.ReplaceOnClick(DoneButtonClickedHandler);
            SetupTabButtons();
        }

        private bool IsCurrentNameValid()
        {
            var currentText = _nameInputField.text;
            return currentText.Length >= _minLength && currentText.Length <= _maxLength;
        }

        private void DoneButtonClickedHandler()
        {
            if (!IsCurrentNameValid()) return;

            if (!ConnectivityStatusManager.ConnectivityReachable)
            {
                _overlayDialogConfig.DisplayType = DisplayType.FloatingText;
                _overlayDialogConfig.TargetTransform = _doneButton.transform;
                _overlayDialogConfig.ShowBackground = true;
                _overlayDialogConfig.TextToDisplay = LocalizationManagerHelper.OfflineConnectionProblemKey;
                _overlayDialogManager.ShowOverlayDialog(_overlayDialogConfig);
                return;
            }
            
            TriggerOnCloseClicked();
        }

        protected override void OnShow()
        {
            base.OnShow();

            _errorHandler.SetActive(false);
            _scrollRect.verticalNormalizedPosition = 1f;
            _nameInputField.interactable = false;

            var displayName = _accountManager.Profile.DisplayName.Trim();
            if (displayName.Length > _maxLength)
            {
                displayName = displayName[.._maxLength];
            }

            RefreshNameColor();
            _nameInputField.text = displayName;
            SelectedDisplayName = displayName;
            SubmitProfileName(displayName);

            _changeNameButton.ReplaceOnClick(EnableInputField);
            _nameInputField.onEndEdit.RemoveAllListeners();
            _nameInputField.onEndEdit.AddListener(SubmitProfileName);

            RefreshAvatar();
            UpdateTabVisuals();
            SetupTabContent();
        }

        private void RefreshNameColor()
        {
            _nameText.color = _accountManager.Profile.IsDisplayNameOverriden ? _overridenNameColor : _defaultNameColor;
        }

        protected override void OnHide()
        {
            base.OnHide();
            _asyncAvatar.ManualStop();
        }

        public void EnableInputField()
        {
            if (!_nameInputField.interactable)
            {
                _errorHandler.SetActive(false);
                _nameInputField.interactable = true;
            }

            if (_nameInputField.isActiveAndEnabled)
            {
                _nameInputField.ActivateInputField();
            }

            _accountManager.Profile.IsDisplayNameOverriden = true;
            RefreshNameColor();
        }

        private void SubmitProfileName(string text)
        {
            var newDisplayName = text;
            newDisplayName = newDisplayName.Trim();

            if (!newDisplayName.IsNullOrEmpty())
            {
                if (newDisplayName.Length < _minLength)
                {
                    _errorHandler.SetActive(true);
                }
                else if (newDisplayName.Length > _maxLength)
                {
                    _errorHandler.SetActive(true);
                }
                else if (newDisplayName != SelectedDisplayName)
                {
                    _errorHandler.SetActive(false);
                    SelectedDisplayName = newDisplayName;
                    StartCoroutine(DelayedInputDisabler());
                }
            }
            else
            {
                _errorHandler.SetActive(true);
                _nameInputField.text = SelectedDisplayName;
                StartCoroutine(DelayedInputDisabler());
            }
        }

        private IEnumerator DelayedInputDisabler()
        {
            yield return null;
            _nameInputField.interactable = false;
        }

        private void FillAvatars()
        {
            ClearCurrentItems();

            var profile = _accountManager.Profile;
            var currentAvatar = profile.Avatar;

            foreach (var defaultAvatar in GenericResourceProvider.DefaultAvatars)
            {
                CreateAvatarItem(defaultAvatar, AvatarSelectedHandler, currentAvatar, false, false);
            }

            var socialAvatarName = GetSocialAvatarName();
            if (!currentAvatar.IsNullOrEmpty() && !GenericResourceProvider.DefaultAvatars.Contains(currentAvatar) && socialAvatarName != currentAvatar)
            {
                CreateAvatarItem(currentAvatar, AvatarSelectedHandler, currentAvatar, false, false);
            }

            if (!socialAvatarName.IsNullOrEmpty())
            {
                CreateAvatarItem(socialAvatarName, SocialAvatarSelectedHandler, currentAvatar, true, false);
            }
        }

        private string GetSocialAvatarName()
        {
            //even if we have user logged in with different platforms having multiple social avatars - we're showing only 1 of them. The facebook one has the most priority.
            var profile = _accountManager.Profile;

            if (!profile.FacebookAvatar.IsNullOrEmpty())
            {
                return profile.FacebookAvatar;
            }
            
            if (!profile.GoogleAvatar.IsNullOrEmpty())
            {
                return profile.GoogleAvatar;
            }
            
            if (!profile.SignedAvatar.IsNullOrEmpty())
            {
                return profile.SignedAvatar;
            }

            return _accountManager.IsAnyPlatformLoggedIn ? string.Empty : GenericResourceProvider.FacebookAvatar;
        }

        private void SocialAvatarSelectedHandler(string avatarName)
        {
            if (avatarName.IsNullOrEmpty())
            {
                return;
            }
            if (avatarName != GenericResourceProvider.FacebookAvatar)
            {
                AvatarSelectedHandler(avatarName);
                return;
            }
            
            Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.EditProfile.CategoryName, DauInteractions.EditProfile.Login, string.Empty));

            if (!ConnectivityStatusManager.ConnectivityReachable)
            {
                _genericModalFactory.ShowNoConnectionModal();
                return;
            }

            var ctrl = _modalsBuilder.CreateModalView<SaveProgressModalController>(ModalsType.SaveProgress);
            ctrl.ShowModal();
        }

        private void AvatarSelectedHandler(string avatarName)
        {
            if (_currentlySelectedAvatar == null)
            {
                Debug.LogError("You should always have _currentlySelectedAvatar");
            }
            else
            {
                if (_currentlySelectedAvatar.ItemUid == avatarName)
                    return;

                _currentlySelectedAvatar.SetAsSelected(false);
            }

            var selected = _avatars.Find(x => x.ItemUid == avatarName);

            _currentlySelectedAvatar = selected;
            _currentlySelectedAvatar.SetAsSelected(true);
            RefreshAvatar(SelectedAvatar);
        }

        private void RefreshAvatar(string avatarName = null)
        {
            if (avatarName.IsNullOrEmpty())
            {
                avatarName = _accountManager.Profile.Avatar;
            }
            _asyncAvatar.ManualStop();
            _asyncAvatar.Setup(new AvatarInfo(avatarName, _accountManager.Profile.Country));
        }

        private void SetupTabButtons()
        {
            _avatarTabButton?.ReplaceOnClick(() => SwitchToTab(AvatarCategories.Avatar));
            _frameTabButton?.ReplaceOnClick(() => SwitchToTab(AvatarCategories.Frames));
            _badgeTabButton?.ReplaceOnClick(() => SwitchToTab(AvatarCategories.Badges));
            _nameTabButton?.ReplaceOnClick(() => SwitchToTab(AvatarCategories.Name));
        }

        private void SwitchToTab(AvatarCategories tab)
        {
            if (_currentTab == tab) return;

            _currentTab = tab;
            UpdateTabVisuals();
            SetupTabContent();
        }

        private void UpdateTabVisuals()
        {
            // Hide all tab contents
            _avatarTabContent?.SetActive(false);
            _frameTabContent?.SetActive(false);
            _badgeTabContent?.SetActive(false);
            _nameTabContent?.SetActive(false);

            // Show current tab content
            switch (_currentTab)
            {
                case AvatarCategories.Avatar:
                    _avatarTabContent?.SetActive(true);
                    break;
                case AvatarCategories.Frames:
                    _frameTabContent?.SetActive(true);
                    break;
                case AvatarCategories.Badges:
                    _badgeTabContent?.SetActive(true);
                    break;
                case AvatarCategories.Name:
                    _nameTabContent?.SetActive(true);
                    break;
            }
        }

        private void SetupTabContent()
        {
            switch (_currentTab)
            {
                case AvatarCategories.Avatar:
                    FillAvatars();
                    break;
                case AvatarCategories.Frames:
                    FillFrames();
                    break;
                case AvatarCategories.Badges:
                    FillBadges();
                    break;
                case AvatarCategories.Name:
                    // Name tab content is handled by existing name input field
                    break;
            }
        }

        private void FillFrames()
        {
            ClearCurrentItems();

            var currentFrame = _accountManager.Profile.AvatarFrame ?? ProfileUtils.DefaultFrameUid;

            foreach (var frameItem in _avatarDecorationManager.FramesList)
            {
                var isUnlocked = IsDecorationUnlocked(frameItem);
                var shouldShow = isUnlocked || frameItem.ShouldShowLockState;

                if (shouldShow)
                {
                    CreateAvatarItem(frameItem.Uid, FrameSelectedHandler, currentFrame, false, !isUnlocked);
                }
            }
        }

        private void FillBadges()
        {
            ClearCurrentItems();

            var currentBadge = _accountManager.Profile.BadgeUid ?? ProfileUtils.DefaultBadgeUid;

            foreach (var badgeItem in _avatarDecorationManager.BadgesList)
            {
                var isUnlocked = IsDecorationUnlocked(badgeItem);
                var shouldShow = isUnlocked || badgeItem.ShouldShowLockState;

                if (shouldShow)
                {
                    CreateAvatarItem(badgeItem.Uid, BadgeSelectedHandler, currentBadge, false, !isUnlocked);
                }
            }
        }



        private bool IsDecorationUnlocked(ProfileCustomizationConfigItem decorationItem)
        {
            if (decorationItem.UnlockConditionId.IsNullOrEmpty())
            {
                return true;
            }

            return false;
        }
        

        private void ClearCurrentItems()
        {
            _avatarsRoot.RemoveAllActiveChilden();
            _avatars.Clear();
            _decorationItems.Clear();
            _currentlySelectedAvatar = null;
            _currentlySelectedFrame = null;
            _currentlySelectedBadge = null;
        }

        private void CreateAvatarItem(string itemUid, Action<string> selectedCallback, string currentSelection, bool placeFirst = false, bool isLocked = false)
        {
            // Use locked prefab if item is locked, otherwise use regular avatar item prefab
            var prefabToUse = isLocked ? (_lockedItemPrefab ?? _avatarItemPrefab) : _avatarItemPrefab;
            if (prefabToUse == null) return;

            UnityEngine.Profiling.Profiler.BeginSample($"Instantiate[{prefabToUse.name}]");
            var go = Instantiate(prefabToUse, _avatarsRoot);
            UnityEngine.Profiling.Profiler.EndSample();
            go.SetActive(true);

            var avatarItem = go.GetComponent<AvatarItem>();
            if (avatarItem == null)
            {
                Debug.LogError($"AvatarItem component not found on {prefabToUse.name}");
                return;
            }

            // Setup the avatar item
            avatarItem.Setup(itemUid, selectedCallback);
            var isCurrent = itemUid == currentSelection;

            // Handle positioning
            if (placeFirst || isCurrent)
            {
                avatarItem.transform.SetAsFirstSibling();
            }

            // Handle selection and add to appropriate collection
            if (isCurrent)
            {
                avatarItem.SetAsSelected(true);
                SetCurrentSelectedItem(avatarItem);
            }

            AddToCurrentCollection(avatarItem);

            // Add visual indicator for locked items if using regular prefab
            if (isLocked && prefabToUse == _avatarItemPrefab)
            {
                AddLockedOverlay(go);
            }
        }

        private void SetCurrentSelectedItem(AvatarItem avatarItem)
        {
            switch (_currentTab)
            {
                case AvatarCategories.Avatar:
                    _currentlySelectedAvatar = avatarItem;
                    break;
                case AvatarCategories.Frames:
                    _currentlySelectedFrame = avatarItem;
                    break;
                case AvatarCategories.Badges:
                    _currentlySelectedBadge = avatarItem;
                    break;
            }
        }

        private void AddToCurrentCollection(AvatarItem avatarItem)
        {
            switch (_currentTab)
            {
                case AvatarCategories.Avatar:
                    _avatars.Add(avatarItem);
                    break;
                case AvatarCategories.Frames:
                case AvatarCategories.Badges:
                    _decorationItems.Add(avatarItem);
                    break;
            }
        }

        private void AddLockedOverlay(GameObject avatarItemGO)
        {
            // Add a simple locked overlay - you can customize this based on your UI design
            var lockIcon = new GameObject("LockOverlay");
            lockIcon.transform.SetParent(avatarItemGO.transform, false);

            var rectTransform = lockIcon.AddComponent<RectTransform>();
            rectTransform.anchorMin = Vector2.zero;
            rectTransform.anchorMax = Vector2.one;
            rectTransform.offsetMin = Vector2.zero;
            rectTransform.offsetMax = Vector2.zero;

            var image = lockIcon.AddComponent<Image>();
            image.color = new Color(0, 0, 0, 0.7f); // Semi-transparent black overlay

            // You could add a lock icon sprite here if available
            // image.sprite = lockIconSprite;
        }

        private void FrameSelectedHandler(string frameUid)
        {
            if (_currentlySelectedFrame != null && _currentlySelectedFrame.ItemUid == frameUid)
                return;

            if (_currentlySelectedFrame != null)
            {
                _currentlySelectedFrame.SetAsSelected(false);
            }

            var selected = _decorationItems.Find(x => x.ItemUid == frameUid);
            _currentlySelectedFrame = selected;
            _currentlySelectedFrame?.SetAsSelected(true);

            // Update avatar preview with new frame
            RefreshAvatarWithDecorations();
        }

        private void BadgeSelectedHandler(string badgeUid)
        {
            if (_currentlySelectedBadge != null && _currentlySelectedBadge.ItemUid == badgeUid)
                return;

            if (_currentlySelectedBadge != null)
            {
                _currentlySelectedBadge.SetAsSelected(false);
            }

            var selected = _decorationItems.Find(x => x.ItemUid == badgeUid);
            _currentlySelectedBadge = selected;
            _currentlySelectedBadge?.SetAsSelected(true);

            // Update avatar preview with new badge
            RefreshAvatarWithDecorations();
        }

        private void RefreshAvatarWithDecorations()
        {
            var avatarName = SelectedAvatar.IsNullOrEmpty() ? _accountManager.Profile.Avatar : SelectedAvatar;
            var frameUid = SelectedFrame.IsNullOrEmpty() ? ProfileUtils.DefaultFrameUid : SelectedFrame;
            var badgeUid = SelectedBadge.IsNullOrEmpty() ? ProfileUtils.DefaultBadgeUid : SelectedBadge;

            _asyncAvatar.ManualStop();
            _asyncAvatar.Setup(new AvatarInfo(avatarName, _accountManager.Profile.Country, frameUid, badgeUid));
        }
    }
}