using System;
using BBB.Audio;
using BBB.Core.ResourcesManager;
using BBB.DI;
using BBB.UI.Core;
using BebopBee.Core.Audio;
using BebopBee.Social;
using Cysharp.Threading.Tasks;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.Quests
{
    public class AvatarItem : ContextedUiBehaviour
    {
        [SerializeField] private AsyncImage _avatarImage;
        [SerializeField] private GameObject _selectedHolder;
        [SerializeField] private Button _button;
        [SerializeField] private Transform _frameContainer;

        /// <summary>
        /// The unique identifier for this item (avatar, frame, badge, etc.)
        /// </summary>
        public string ItemUid { get; private set; }
        private IAssetsManager _assetsManager;
        private Action<string> _selectedCallback;
        private GameObject _loadedFramePrefab;

        protected override void InitWithContextInternal(IContext context)
        {
            _assetsManager = context.Resolve<IAssetsManager>();

            _button.ReplaceOnClick(() => _selectedCallback?.Invoke(ItemUid));
        }

        public void Setup(string itemUid, Action<string> selectedCallback)
        {
            SetupAsAvatar(itemUid, selectedCallback);
        }

        public void SetupAsAvatar(string itemUid, Action<string> selectedCallback)
        {
            LazyInit();

            ItemUid = itemUid;
            _selectedCallback = selectedCallback;

            SetAsSelected(false);
            _avatarImage.Load(itemUid, SocialConstants.AvatarsTag, error =>
            {
                if (_avatarImage)
                {
                    _assetsManager
                        .LoadSpriteAsync(GenericResourceProvider.FallbackOfflineAvatar)
                        .ContinueWith(sprite => _avatarImage.ReplaceSprite(sprite));
                }
            });
        }

        public void SetupAsFrame(string frameUid, Action<string> selectedCallback)
        {
            LazyInit();

            ItemUid = frameUid;
            _selectedCallback = selectedCallback;

            SetAsSelected(false);
            LoadFramePrefab(frameUid);
        }

        public void SetupAsBadge(string badgeUid, Action<string> selectedCallback)
        {
            LazyInit();

            ItemUid = badgeUid;
            _selectedCallback = selectedCallback;

            SetAsSelected(false);
            _avatarImage.Load(badgeUid, "badges", error =>
            {
                Debug.LogWarning($"Failed to load badge image for {badgeUid}: {error}");
            });
        }

        public void SetupAsNameStyle(string nameStyleUid, Action<string> selectedCallback)
        {
            LazyInit();

            ItemUid = nameStyleUid;
            _selectedCallback = selectedCallback;

            SetAsSelected(false);
            _avatarImage.Load(nameStyleUid, "namestyles", error =>
            {
                Debug.LogWarning($"Failed to load name style image for {nameStyleUid}: {error}");
            });
        }

        private async void LoadFramePrefab(string frameUid)
        {
            // Clear any existing frame prefab
            ClearLoadedFrame();

            // Load the frame prefab asynchronously
            var framePrefab = await _assetsManager.LoadPrefabAsync(frameUid);
            if (framePrefab != null && _frameContainer != null)
            {
                _loadedFramePrefab = Instantiate(framePrefab, _frameContainer);
                _loadedFramePrefab.SetActive(true);

                // Find the AsyncImage in the loaded frame prefab and load the frame image
                var frameAsyncImage = _loadedFramePrefab.GetComponentInChildren<AsyncImage>();
                if (frameAsyncImage != null)
                {
                    frameAsyncImage.Load(frameUid, "frames", error =>
                    {
                        Debug.LogWarning($"Failed to load frame image for {frameUid}: {error}");
                    });
                }
            }
        }

        private void ClearLoadedFrame()
        {
            if (_loadedFramePrefab != null)
            {
                DestroyImmediate(_loadedFramePrefab);
                _loadedFramePrefab = null;
            }
        }

        public void SetAsSelected(bool selected)
        {
            if (_selectedHolder != null)
            {
                AudioProxy.PlaySound(GenericSoundIds.GenericButtonTap);
                _selectedHolder.SetActive(selected);
            }
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();

            if (_avatarImage != null)
            {
                _avatarImage.Stop();
            }

            ClearLoadedFrame();
        }
    }
}