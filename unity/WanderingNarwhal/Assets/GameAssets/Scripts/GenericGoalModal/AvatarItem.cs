using System;
using BBB.Audio;
using BBB.Core.ResourcesManager;
using BBB.DI;
using BBB.UI.Core;
using BebopBee.Core.Audio;
using BebopBee.Social;
using Cysharp.Threading.Tasks;
using UnityEngine;
using UnityEngine.UI;
using BBB;

namespace BBB.Quests
{
    public class AvatarItem : ContextedUiBehaviour
    {
        [SerializeField] private AsyncImage _avatarImage;
        [SerializeField] private GameObject _selectedHolder;
        [SerializeField] private Button _button;
        [SerializeField] private AsyncLoadablePrefab _framePrefab;

        /// <summary>
        /// The unique identifier for this item (avatar, frame, badge, etc.)
        /// </summary>
        public string ItemUid { get; private set; }
        private IAssetsManager _assetsManager;
        private Action<string> _selectedCallback;
        private AvatarFrameView _avatarFrameView;

        protected override void InitWithContextInternal(IContext context)
        {
            _assetsManager = context.Resolve<IAssetsManager>();

            _button.ReplaceOnClick(() => _selectedCallback?.Invoke(ItemUid));
        }

        public void Setup(string itemUid, Action<string> selectedCallback)
        {
            SetupAsAvatar(itemUid, selectedCallback);
        }

        public void SetupAsAvatar(string itemUid, Action<string> selectedCallback)
        {
            LazyInit();

            ItemUid = itemUid;
            _selectedCallback = selectedCallback;

            SetAsSelected(false);
            _avatarImage.Load(itemUid, SocialConstants.AvatarsTag, error =>
            {
                if (_avatarImage)
                {
                    _assetsManager
                        .LoadSpriteAsync(GenericResourceProvider.FallbackOfflineAvatar)
                        .ContinueWith(sprite => _avatarImage.ReplaceSprite(sprite));
                }
            });
        }

        public void SetupAsFrame(string frameUid, Action<string> selectedCallback)
        {
            LazyInit();

            ItemUid = frameUid;
            _selectedCallback = selectedCallback;

            SetAsSelected(false);

            if (_framePrefab != null && !frameUid.IsNullOrEmpty())
            {
                _framePrefab.Show(frameUid, OnFrameCreated);
            }
        }

        private void OnFrameCreated(GameObject frameObject)
        {
            _avatarFrameView = frameObject.GetComponent<AvatarFrameView>();
            if (_avatarFrameView != null)
            {
                // Setup the frame with a placeholder avatar or empty
                _avatarFrameView.SetupAvatar(string.Empty);
            }
        }

        public void SetupAsBadge(string badgeUid, Action<string> selectedCallback)
        {
            LazyInit();

            ItemUid = badgeUid;
            _selectedCallback = selectedCallback;

            SetAsSelected(false);
            _avatarImage.Load(badgeUid, "badges", error =>
            {
                Debug.LogWarning($"Failed to load badge image for {badgeUid}: {error}");
            });
        }

        public void SetupAsNameStyle(string nameStyleUid, Action<string> selectedCallback)
        {
            LazyInit();

            ItemUid = nameStyleUid;
            _selectedCallback = selectedCallback;

            SetAsSelected(false);
            _avatarImage.Load(nameStyleUid, "namestyles", error =>
            {
                Debug.LogWarning($"Failed to load name style image for {nameStyleUid}: {error}");
            });
        }

        public void ManualStop()
        {
            if (_avatarImage != null)
            {
                _avatarImage.Stop();
            }

            if (_framePrefab != null)
            {
                if (_avatarFrameView != null)
                {
                    _avatarFrameView.Clear();
                    _avatarFrameView = null;
                }
                _framePrefab.Clear();
            }
        }

        public void SetAsSelected(bool selected)
        {
            if (_selectedHolder != null)
            {
                AudioProxy.PlaySound(GenericSoundIds.GenericButtonTap);
                _selectedHolder.SetActive(selected);
            }
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            ManualStop();
        }
    }
}