using System.Collections.Generic;
using BBB;
using BBB.Match3;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class SimpleTile : Tile
    {
        public SimpleTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            AddMandatoryParamsTile();
            State |= TileState.None;
            SortOrder = 10;
        }
    }
}