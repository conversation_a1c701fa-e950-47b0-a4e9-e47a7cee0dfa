using System.Collections.Generic;
using BBB;
using BBB.Core;
using BBB.DI;
using BBB.Map;
using FBConfig;
using GameAssets.Scripts.Map;
using GameAssets.Scripts.Tripstagram;

namespace GameAssets.Scripts.DailyTask
{
    public class EpisodicTaskCompletedTaskProgressTracker : TaskProgressTracker
    {
        public override string TaskType => "Meta";

        private IEventDispatcher _eventDispatcher;
        private IEpisodeTaskManager _episodeTaskManager;
        private IEpisodicScenesManager _episodicScenesManager;
        private IPlayerManager _playerManager;
        private IScreensManager _screensManager;
        
        public override void Init(IContext context)
        {
            _eventDispatcher = context.Resolve<IEventDispatcher>();
            _episodeTaskManager = context.Resolve<IEpisodeTaskManager>();
            _episodicScenesManager = context.Resolve<IEpisodicScenesManager>();
            
            _eventDispatcher.RemoveListener<EpisodicTaskCompletedEvent>(EpisodicTaskCompletedEventHandler);
            _eventDispatcher.AddListener<EpisodicTaskCompletedEvent>(EpisodicTaskCompletedEventHandler);
            _playerManager = context.Resolve<IPlayerManager>();
            _screensManager = context.Resolve<IScreensManager>();
        }

        public override void DeInit()
        {
            base.DeInit();
            _eventDispatcher.RemoveListener<EpisodicTaskCompletedEvent>(EpisodicTaskCompletedEventHandler);
        }

        public override void OnFlowRequested()
        {
            var viewEpisodicSceneEvent = _eventDispatcher.GetMessage<BuildEpisodicSceneEvent>();
            viewEpisodicSceneEvent.Set(_playerManager.Player.CurrentEpisodeScene, false, _screensManager.GetCurrentScreenType());
            _eventDispatcher.TriggerEvent(viewEpisodicSceneEvent);
        }

        private void EpisodicTaskCompletedEventHandler(EpisodicTaskCompletedEvent ev)
        {
            AddProgress(1);
        }

        public override bool CheckIfTaskIsCompletable(DailyTasksConfigT taskConfig)
        {
            int totalTasksAvailable = 0;
            var remainingScenesUidList = _episodicScenesManager.GetListOfRemainingSceneUid();
            foreach (var scene in remainingScenesUidList)
            {
                totalTasksAvailable += _episodeTaskManager.GetNumberOfRemainingTasks(scene);
                if (totalTasksAvailable >= taskConfig.Amount)
                    return true;
            }

            return false;
        }
    }
}