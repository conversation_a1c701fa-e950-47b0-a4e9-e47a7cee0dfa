using System;
using BBB;
using BBB.Core;
using BBB.DI;
using BBB.Quests;
using BBB.Screens;
using Bebopbee.Core.Extensions.Unity;
using BebopBee.UnityEngineExtensions;
using JetBrains.Annotations;
using UnityEngine;
using UnityEngine.UI;

namespace GameAssets.Scripts.CurrenciesRewardModalUI
{
    public class CurrenciesRewardModalViewPresenter : ModalsViewPresenter, ICurrenciesRewardModalViewPresenter
    {
        private static readonly int CleanId = Animator.StringToHash("Clean");
        private const string DefaultHeaderLocKey = "REWARD_TITLE_CONGRATULATIONS";

        public event Action AdRequested;
        public event Action OnCloseButtonClicked;

        [SerializeField] private GenericRewardScreen _genericRewardScreen;
        [SerializeField] private Button _adButton;
        [SerializeField] private GameObject _adButtonHolder;
        [SerializeField] private Animator _animator;
        [SerializeField] private GameObject[] _cardsInterruptedHoldersToDisable;

        private VideoAdManager _videoAdManager;
        private FBConfig.IAPStoreMarketItemConfig _iapStoreMarketItemConfig;
        private CurrenciesRewardViewModel _viewModel;

        private bool _rewardInitialized = false;
        private Action _onHide;

        protected override void OnContextInitialized(IContext context)
        {
            _adButton.ReplaceOnClick(() =>
            {
                AdRequested?.Invoke();
                RefreshAd();
            });

            base.OnContextInitialized(context);
            _genericRewardScreen.Init(context);

            _videoAdManager = context.Resolve<VideoAdManager>();
        }

        /// <summary>
        /// Setup the modal view.
        /// </summary>
        /// <param name="viewModel"></param>
        /// <param name="onHide"> On Hide callback. May be called later than OnClose because of hide animation. </param>
        public void SetupInitialParams(CurrenciesRewardViewModel viewModel, Action onHide)
        {
            _genericRewardScreen.SetupHeader(viewModel.TitleText.IsNullOrEmpty() ? DefaultHeaderLocKey : viewModel.TitleText, viewModel.TitleTextLocParams);
            _genericRewardScreen.SetupSubtitle(viewModel.SubtitleText, viewModel.SubtitleTextActiveState, viewModel.SubtitleTextLocParams);

            _viewModel = viewModel;
            _onHide = onHide;

            RefreshAd();
        }

        public void RefreshAd(bool? available = null)
        {
            var adAvailable = available ?? _videoAdManager.IsAdAvailable(_viewModel.AdPlacement, _viewModel.AdPlacement);
            _adButtonHolder.SetActive(adAvailable);
        }

        protected override void OnShow()
        {
            base.OnShow();

            _cardsInterruptedHoldersToDisable.Enable(true);

            if (!_rewardInitialized)
            {
                SetupReward();
            }
        }

        public void SetupReward()
        {
            if (_viewModel == null || _rewardInitialized)
            {
                BDebug.Log($"<color=red>SetupReward failed " +
                           $"view model is null : {_viewModel == null} " +
                           $"iap config is null : {_iapStoreMarketItemConfig} " +
                           $" reward initialized : {_rewardInitialized} </color>");

                _genericRewardScreen.SetupButtonsOnly();
                return;
            }

            _rewardInitialized = true;
            _genericRewardScreen.SetupReward(_viewModel.RewardDict, () => { _onHide?.Invoke(); }, _viewModel.RewardViewOverride, CloseClickedHandler);
        }

        private void CloseClickedHandler()
        {
            OnCloseButtonClicked.SafeInvoke();
        }

        public void Clear()
        {
            _viewModel = null;
            _iapStoreMarketItemConfig = default;
            _rewardInitialized = false;
            _genericRewardScreen.Clear();
        }

        [UsedImplicitly]
        private void HideFx()
        {
            _cardsInterruptedHoldersToDisable.Enable(false);
        }

        public void FadeOut()
        {
            _animator.ResetAllParameters();
            _animator.SetTrigger(CleanId);
            _genericRewardScreen.FadeOut();
        }

        public void FadeIn()
        {
            _genericRewardScreen.FadeIn();
        }

        public void StartOutroAnimation()
        {
            _genericRewardScreen.StartOutroAnimation();
        }
    }
}