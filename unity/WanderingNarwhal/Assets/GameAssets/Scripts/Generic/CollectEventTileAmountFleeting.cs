using TMPro;
using UnityEngine;
using BBB.Core;
using UnityEngine.UI;

namespace BBB
{
    public class CollectEventTileAmountFleeting : ActivePoolItem
    {
        [SerializeField] private TextMeshProUGUI _amountText;
        [SerializeField] private Image _sprite;
        private Animation _animation;

        private Material _defaultMaterial;

        public override void OnInstantiate()
        {
            if (!Initialized)
            {
                _animation = gameObject.GetComponent<Animation>();
            }
            _defaultMaterial = _amountText.fontSharedMaterial;
            base.OnInstantiate();
        }

        public override void OnRelease()
        {
            base.OnRelease();
            _amountText.fontMaterial = _defaultMaterial;
            if (!Initialized)
            {
                if (_animation)
                {
                    _animation.Rewind();
                }
            }
        }

        public void Setup(Sprite sprite, int amount)
        {
            _sprite.sprite = sprite;
            _sprite.preserveAspect = true;
            _amountText.text = $"+ {amount}";
        }

        public void SetMaterial(Material material)
        {
            _amountText.fontMaterial = material;
        }

        public void OnAnimationEnded()
        {
            gameObject.Release();
        }
    }
}