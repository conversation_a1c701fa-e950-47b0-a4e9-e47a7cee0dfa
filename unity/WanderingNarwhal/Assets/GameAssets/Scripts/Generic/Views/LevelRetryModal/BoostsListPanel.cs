using System;
using System.Collections.Generic;
using BBB;
using BBB.DI;
using BBB.Map;
using FBConfig;
using PBGame;
using UnityEngine;
using UnityEngine.Assertions;
using UnityEngine.UI;

namespace GameAssets.UI.Level.Scripts.Views
{
    /// <summary>
    /// Component that makes pre-level m3 boosters buttons functional (and placed correctly).
    /// </summary>
    public class BoostsListPanel : BbbMonoBehaviour
    {
        // Custom simple horizontal layout parameters:
        [SerializeField]
        private RectTransform _root;

        [SerializeField]
        private float _buttonWidth = 50f;

        [SerializeField]
        private float _spacing = 10f;

        [SerializeField]
        private float _margin = 5f;

        [SerializeField]
        private Button _freeBoostButton;

        [SerializeField]
        private BoostListItem _boostButtonTemplate;

        private Transform _boostButtonsParent;

        private readonly List<BoostListItem> _boostButtonsInstances = new();

        /// <summary>
        /// Cached delegate to avoid allocation on every refresh.
        /// </summary>
        private Action<BoosterConfig> _boosterClickDelegate;

        public Action<BoosterConfig> onBoosterPurchaseClickEvent;

        private IPlayerManager _playerManager;
        private IInventory _inventory => _playerManager.PlayerInventory;
        private IBoosterManager _boosterManager;
        private ILocalizationManager _localizationManager;
        private ILockManager _lockManager;
        private FBConfig.ProgressionLevelConfig _levelConfig;
        private LevelState _levelState;
        private IConfig _config;

        private readonly List<BoosterConfig> _boosterConfigs = new();
        private VideoAdManager _videoAdManager;

        private void Awake()
        {
            _boostButtonTemplate.gameObject.SetActive(false);
        }

        public void Init(IContext context)
        {
            _videoAdManager = context.Resolve<VideoAdManager>();
            _playerManager = context.Resolve<IPlayerManager>();
            _boosterManager = context.Resolve<IBoosterManager>();
            _localizationManager = context.Resolve<ILocalizationManager>();
            _lockManager = context.Resolve<ILockManager>();
            _config = context.Resolve<IConfig>();
        }

        [ContextMenu("Refresh")]
        public void RefreshPanel()
        {
            if (_inventory == null) return;
            if (_lockManager == null) return;
            var startupBoosts = _levelConfig.TrueStartupBoosts();
            if (startupBoosts == null) return;

            _boosterConfigs.Clear();
            foreach (var boost in startupBoosts)
            {
                var boostConfig = _config.GetFromDictionary<BoosterConfig>(boost);

                _boosterConfigs.Add(boostConfig);
            }

            int boostsCount = _boosterConfigs.Count;
            _boostButtonsParent = _boostButtonTemplate.transform.parent;
            Util.SetListItemInstancesExactCount(_boostButtonsInstances, boostsCount, _boostButtonTemplate, _boostButtonsParent);
            bool isAdButtonActive = _videoAdManager.IsAdAvailable(nameof(BoostsListPanel), VideoAdManager.LevelBooster);
            SetupButtonsLayout(boostsCount, isAdButtonActive);
            SetupBoosterButtons();
        }

        private void SetupBoosterButtons()
        {
            if (_boosterClickDelegate == null)
            {
                _boosterClickDelegate = OnBoosterPuchaseClick;
            }

            Assert.IsTrue(_boosterConfigs.Count == _boostButtonsInstances.Count, "Configs don't match buttons");

            for (int i = 0; i < _boosterConfigs.Count; i++)
            {
                var boostConfig = _boosterConfigs[i];
                
                var btn = _boostButtonsInstances[i];
                btn.gameObject.SetActive(true);
                btn.Init(boostConfig, _inventory, _boosterManager, _lockManager, _localizationManager, _boosterClickDelegate);
            }
        }

        private void OnBoosterPuchaseClick(BoosterConfig boosterConfig)
        {
            onBoosterPurchaseClickEvent?.Invoke(boosterConfig);
        }

        private void SetupButtonsLayout(int buttonsCount, bool isVideoAdButtonActive)
        {
            var totalButtonsCount = buttonsCount + (isVideoAdButtonActive ? 1 : 0);
            var widthForButtons = _buttonWidth * totalButtonsCount + Mathf.Max(0, totalButtonsCount - 1) * _spacing;
            var totalParentWidth = _margin * 2 + widthForButtons;
            _root.sizeDelta = new Vector2(totalParentWidth, _root.sizeDelta.y);

            int i = 0;
            if (isVideoAdButtonActive)
            {
                i = 1;
                var freeBtn = _freeBoostButton.GetComponent<RectTransform>();
                freeBtn.anchoredPosition = new Vector2(-widthForButtons * 0.5f + _buttonWidth * 0.5f, 0f);
                _freeBoostButton.gameObject.SetActive(true);
            }
            else
            {
                _freeBoostButton.gameObject.SetActive(false);
            }

            int btnIndex = 0;
            for (; i < totalButtonsCount; i++, btnIndex++)
            {
                var btn = _boostButtonsInstances[btnIndex];
                btn.RectTransformRef.anchoredPosition = new Vector2(-widthForButtons * 0.5f + _buttonWidth * (i + 0.5f) + _spacing * i, 0f);
            }
        }
    }
}