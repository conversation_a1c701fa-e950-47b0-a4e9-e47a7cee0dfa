using System;
using BBB.Match3.Renderer;
using UnityEngine;

namespace BBB.UI.Level
{
    /// <summary>
    /// Scriptable object that provides Match3Help content data which will be displayed when target tile is clicked. 
    /// </summary>
    [CreateAssetMenu(fileName = "TileMechanicHelpSettings", menuName = "BBB/TileMechanicHelpSettings", order = 0)]
    public class TileMechanicHelpListSettings : ScriptableObject
    {
        public CellMechanicHelpInfo[] CellInfo = new CellMechanicHelpInfo[0];
        public TileMechanicHelpInfo[] TileInfo = new TileMechanicHelpInfo[0];
    }

    [Serializable]
    public class CellMechanicHelpInfo
    {
        public bool IsFirstPriority;
        public CellLayerState CellState;
        public string NameLocKey;
        public string DescriptionLocKey;
        public float PreviewScaleMlt = 2f;

        public HelpTriggerType[] TriggerTypes = new[] { HelpTriggerType.DoubleTap, HelpTriggerType.LongHold };

        public HelpTriggerType TriggerType
        {
            get
            {
                HelpTriggerType result = (HelpTriggerType)0;
                foreach (var t in TriggerTypes)
                {
                    result |= t;
                }

                return result;
            }
        }
    }

    [Serializable]
    public class TileMechanicHelpInfo
    {
        /// <summary>
        /// If tile contains multiple views, the view with the FirstPriority will trigger first.
        /// </summary>
        /// <remarks>
        /// For examples Chains view should be taken first than anything below it.
        /// </remarks>
        [Tooltip("For examples Chains view should be taken first than anything below it.")]
        public bool IsFirstPriority;

        [TileLayerStateName]
        public int TileLayerStateIndex;
        public string NameLocKey;
        public string DescriptionLocKey;
        public float PreviewScaleMlt = 2f;

        public HelpTriggerType[] TriggerTypes = new[] { HelpTriggerType.DoubleTap, HelpTriggerType.LongHold };

        public HelpTriggerType TriggerType
        {
            get
            {
                HelpTriggerType result = (HelpTriggerType)0;
                foreach (var t in TriggerTypes)
                {
                    result |= t;
                }

                return result;
            }
        }

        public TileLayerState LayerState
        {
            get { return TileLayerStateIndex.GetTileLayerFromInt(); }
        }
    }
}