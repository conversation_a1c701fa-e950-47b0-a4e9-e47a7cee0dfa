using System;
using BBB.DI;
using UnityEngine;
using UnityEngine.UI;
using GameAssets.Scripts.UI.OverlayDialog;

namespace BBB
{
    public class DynamicFloatingTextEffect : FloatingTextEffectBase, IOverlayDialogController
    {
        [SerializeField] private LocalizedTextPro _floatingTextLocalized;
        [SerializeField] private HorizontalLayoutGroup _containerLayoutGroup;
        [SerializeField] private GameObject _background;
        [SerializeField] private int _minContainerWidth;
        [SerializeField] private int _maxContainerWidth;

        private RectTransform _rootRectTransform;
        private Vector3 _screenLeftBottom;
        private Vector3 _screenRightTop;
        private int _defaultSortOrder;
        private Transform _targetTransform;
        
        public event Action<bool> Shown;
        public event Action<Transform, IOverlayDialogController> Destroyed;

        protected override void Awake()
        {
            base.Awake();
            _rootRectTransform = _rootObject as RectTransform;
        }
        
        public void Init(IContext context)
        {
            
        }

        protected override void AfterRootVisible()
        {
            SetWidth();
            KeepWithinScreen();
        }

        private void SetWidth()
        {
            var textWidth = _floatingText.preferredWidth;
            var containerPaddingsWidth = _containerLayoutGroup.padding.left + _containerLayoutGroup.padding.right;
            var containerWidth = textWidth + containerPaddingsWidth;

            containerWidth = Mathf.Clamp(containerWidth, _minContainerWidth, _maxContainerWidth);
            textWidth = containerWidth - containerPaddingsWidth;

            ChangeRectTransformWidth(_rootRectTransform, containerWidth);
            ChangeRectTransformWidth(FloatingTextRectTransform, textWidth);

            LayoutRebuilder.ForceRebuildLayoutImmediate(_rootRectTransform);
        }

        private static void ChangeRectTransformWidth(RectTransform rectTransform, float width)
        {
            var size = rectTransform.sizeDelta;
            size.x = width;
            rectTransform.sizeDelta = size;
        }

        private void KeepWithinScreen()
        {
            var root = transform.parent.RectTransform();
            var rootCorners = new Vector3[4];
            root.GetWorldCorners(rootCorners);

            _screenLeftBottom = rootCorners[0];
            _screenRightTop = rootCorners[2];

            var halfRootSize = _rootRectTransform.sizeDelta / 2;
            var rootLeftBottom = _rootObject.parent.TransformPoint(-halfRootSize);
            var rootRightTop = _rootObject.parent.TransformPoint(halfRootSize);

            var offset = Vector3.zero;

            if (rootLeftBottom.x < _screenLeftBottom.x) //left
                offset.x = _screenLeftBottom.x - rootLeftBottom.x;
            if (rootRightTop.x > _screenRightTop.x) //right
                offset.x = _screenRightTop.x - rootRightTop.x;
            if (rootLeftBottom.y < _screenLeftBottom.y) //bottom
                offset.y = _screenLeftBottom.y - rootLeftBottom.y;
            if (rootRightTop.y > _screenRightTop.y) //top
                offset.y = _screenRightTop.y - rootRightTop.y;

            transform.position += offset;
        }

        public void ShowOverlayDialog(OverlayDialogConfig overlayDialogConfig,
            Action<IOverlayDialogController, Transform> callback)
        {
            Shown?.Invoke(true);

            _background.SetActive(overlayDialogConfig.ShowBackground);
            _floatingTextLocalized.SetTextId(overlayDialogConfig.TextToDisplay, overlayDialogConfig.TextArgs);

            _targetTransform = overlayDialogConfig.TargetTransform;

            PlayFloatingAnimation(overlayDialogConfig.TargetTransform,
                onComplete: _ => { callback?.Invoke(this, _targetTransform); });
        }

        public void HideRewardInfo(Action<IOverlayDialogController, Transform> onHiddenCallback, bool hideFast = false)
        {
            HideFloatingAnimation(_ =>
            {
                onHiddenCallback?.Invoke(this, _targetTransform);
            });
        }

        public void ToggleOverlayDialog(OverlayDialogConfig overlayDialogConfig,
            Action<IOverlayDialogController, Transform> callback)
        {
            ShowOverlayDialog(overlayDialogConfig, callback);
        }
        
        protected override void OnDestroy()
        {
            base.OnDestroy();
            
            if (_targetTransform != null)
            {
                Destroyed.SafeInvoke(_targetTransform, this);
            }
        }
    }
}