using System;
using System.Text;
using BBB;
using BBB.Screens;
using BBB.UI;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace GameAssets.Scripts.Loading.Modals
{
    public class AssetsLoadingModalViewPresenter : ModalsViewPresenter, IAssetsLoadingModalViewPresenter
    {
        [SerializeField] private Button _goButton;
        [SerializeField] private MaterialSwapper _materialSwapper;
        [SerializeField] private ProgressBar _progressBar;
        [SerializeField] private TextMeshProUGUI _progressText;
        [SerializeField] private LocalizedTextPro _goButtonText;
        [SerializeField] private string _okTextId;
        [SerializeField] private string _continueTextId;

        private ISpecializedResourceManager _specializedResourceManager;
        private readonly StringBuilder _progressTextBuilder = new();
        private float _cachedProgress = -1f;

        public event Action GoButtonClicked;

        protected override void Awake()
        {
            base.Awake();
            _goButton.ReplaceOnClick(() => GoButtonClicked.SafeInvoke());
            _materialSwapper.StoreOriginalValues();
        }

        public void Setup(ISpecializedResourceManager resourceManager)
        {
            _specializedResourceManager = resourceManager;
            _goButtonText.SetTextId(_okTextId);
            CloseButton.gameObject.SetActive(true);
            EnableButton();
        }

        public void ConfigureForTransition()
        {
            _goButtonText.SetTextId(_continueTextId);
            CloseButton.gameObject.SetActive(false);
            DisableButton();
        }

        private void EnableButton()
        {
            _materialSwapper.SetOriginalValue();
            _goButton.interactable = true;
        }

        private void DisableButton()
        {
            _materialSwapper.SetNewValue();
            _goButton.interactable = false;
        }

        private void Update()
        {
            var progress = _specializedResourceManager.Progress();
            if (Mathf.Approximately(progress, _cachedProgress)) return;
            _cachedProgress = progress;
            if (progress >= 1f)
            {
                EnableButton();
            }
            _progressBar.SetProgress(progress);
            _progressTextBuilder.Clear();
            _progressTextBuilder.Append((int)(progress * 100));
            _progressTextBuilder.Append("%");
            _progressText.text = _progressTextBuilder.ToString();
        }
    }
}
