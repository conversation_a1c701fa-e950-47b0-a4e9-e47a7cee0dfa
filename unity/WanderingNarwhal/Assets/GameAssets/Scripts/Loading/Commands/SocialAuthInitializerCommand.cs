using System;
using BBB;
using BBB.Core;
using BBB.Core.Crash;
using BBB.DI;
using BebopBee;
using BBB.Social.SignIn;
using Cysharp.Threading.Tasks;

namespace Loading.Commands
{
    public class SocialAuthInitializerCommand: CommandBase
    {
        protected override void CommandExecutionStart(IContext context)
        {
            base.CommandExecutionStart(context);

            var signInManager = context.Resolve<ISignInManager>();
            try
            {
                signInManager.Initialize().Forget();
            }
            catch (Exception ex)
            {
                BDebug.LogError(LogCat.Login, $"Failed to initialize social authentication manager: {ex}");
            }
            CurrentStatus = CommandStatus.Success;
        }        
    }
}