
using BBB.DI;
using BebopBee;

namespace BBB.Commands
{
    public class UpdatePlayerCommand : CommandBase
    {
        protected override void CommandExecutionStart(IContext context)
        {
            var accountManager = context.Resolve<IAccountManager>(null, true);
            if (accountManager == null)
            {
                CurrentStatus = CommandStatus.Success;
                return;
            }
            
            accountManager.TryUpdatePlayer(()=> {
                CurrentStatus = CommandStatus.Success;
            });
        }
    }
}