using BBB.DailyTrivia;
using BBB.DI;

namespace BBB.UI
{
    public class DailyTriviaHudAssetReference : IGenericHudDrivenAssetReference
    {
        public string AssetName { get; set; }

        public bool ShouldBeInstantiated(IContext context)
        {
            return context.Resolve<DailyTriviaManager>()
                .ShouldShowHud(context.Resolve<IScreensManager>().GetCurrentScreenType());
        }
    }
}