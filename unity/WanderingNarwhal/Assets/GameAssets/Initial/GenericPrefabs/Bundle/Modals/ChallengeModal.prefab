%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &36812452609103481
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8493804824271714284}
  - component: {fileID: 6406134632438488250}
  - component: {fileID: 4086093702387507711}
  m_Layer: 5
  m_Name: MainBG
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &8493804824271714284
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 36812452609103481}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6099812445402726806}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: -1, y: -1.0000076}
  m_SizeDelta: {x: 6, y: 10}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &6406134632438488250
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 36812452609103481}
  m_CullTransparentMesh: 1
--- !u!114 &4086093702387507711
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 36812452609103481}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.15686275, g: 0.21176472, b: 0.2784314, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: 1135918bd1512474b9ffe64850dcbea7, type: 3}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 0.85
--- !u!1 &155875773280007916
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 133680448028257830}
  - component: {fileID: 1010941682412394464}
  - component: {fileID: 5346509976305093505}
  - component: {fileID: 5042500803492736841}
  m_Layer: 5
  m_Name: SelectedBGBase
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &133680448028257830
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 155875773280007916}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7021039355582748800}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: -1, y: 1.5}
  m_SizeDelta: {x: -2, y: -3}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &1010941682412394464
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 155875773280007916}
  m_CullTransparentMesh: 1
--- !u!114 &5346509976305093505
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 155875773280007916}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: 1135918bd1512474b9ffe64850dcbea7, type: 3}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1.1
--- !u!114 &5042500803492736841
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 155875773280007916}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 45facfdc6a639f041b007c036dc527b8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _gradientType: 1
  _blendMode: 2
  _offset: 0
  _effectGradient:
    serializedVersion: 2
    key0: {r: 0.25882354, g: 0.47058827, b: 0.76470596, a: 1}
    key1: {r: 0.27450982, g: 0.54901963, b: 0.9450981, a: 1}
    key2: {r: 0, g: 0, b: 0, a: 0}
    key3: {r: 0, g: 0, b: 0, a: 0}
    key4: {r: 0, g: 0, b: 0, a: 0}
    key5: {r: 0, g: 0, b: 0, a: 0}
    key6: {r: 0, g: 0, b: 0, a: 0}
    key7: {r: 0, g: 0, b: 0, a: 0}
    ctime0: 0
    ctime1: 65535
    ctime2: 0
    ctime3: 0
    ctime4: 0
    ctime5: 0
    ctime6: 0
    ctime7: 0
    atime0: 0
    atime1: 65535
    atime2: 0
    atime3: 0
    atime4: 0
    atime5: 0
    atime6: 0
    atime7: 0
    m_Mode: 0
    m_ColorSpace: 0
    m_NumColorKeys: 2
    m_NumAlphaKeys: 2
--- !u!1 &233999395194368759
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6737153281501167778}
  - component: {fileID: 4253908801930210614}
  - component: {fileID: 8838485052250390168}
  - component: {fileID: 3220649121975863739}
  - component: {fileID: 3424739413635948071}
  - component: {fileID: 8970484364218103078}
  m_Layer: 5
  m_Name: ChallengeModal
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &6737153281501167778
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 233999395194368759}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 524870086518532278}
  - {fileID: 3182571624525991704}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &4253908801930210614
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 233999395194368759}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cc834fbaaa334bf1b6fd271247c4f060, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  CloseButton: {fileID: 5167659423323288036}
  _paletteApplier: {fileID: 0}
  _infoButton: {fileID: 6224017399095278070}
  _newChallengesHolders:
  - {fileID: 8592886731957071906}
  _activeGamesHolders:
  - {fileID: 9055978630373928171}
  _newChallengesPanel: {fileID: 2179097678281023694}
  _activeChallengesPanel: {fileID: 6019110571210670978}
  _progressText: {fileID: 2558912525217031814}
  _progressBar: {fileID: 1280380112848891675}
  _divisionsParent: {fileID: 2653116513856503030}
  _divisionPrefab: {fileID: 3059767998238953953, guid: 94facb55d1a83413d80592b0881dd747,
    type: 3}
  _progressNumberText: {fileID: 4817809125322932066}
  _loadingHolders:
  - {fileID: 1179461452442970556}
  - {fileID: 3824613319224366770}
--- !u!223 &8838485052250390168
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 233999395194368759}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 2
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 1
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_VertexColorAlwaysGammaSpace: 0
  m_AdditionalShaderChannelsFlag: 25
  m_UpdateRectTransformForStandalone: 0
  m_SortingLayerID: 0
  m_SortingOrder: 121
  m_TargetDisplay: 0
--- !u!114 &3220649121975863739
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 233999395194368759}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
--- !u!95 &3424739413635948071
Animator:
  serializedVersion: 7
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 233999395194368759}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: c2a78409df6371f40b23f395b9499de1, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_AnimatePhysics: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!114 &8970484364218103078
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 233999395194368759}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e6915c4f617c4447bdaf2e47e4cb32f3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _animationCurve:
    serializedVersion: 2
    m_Curve: []
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  _finalPosY: 300
  _startPosY: 2000
  _duration: 0.3
  _animators:
  - {fileID: 3424739413635948071}
  _useSlideAnim: 0
  _instantHide: 0
  _fallbackHidingTimerDuration: 2
  _enableAnimatorsOnlyForAnimation: 0
--- !u!1 &281055667473413853
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 837379061992744721}
  - component: {fileID: 5451908227930356755}
  - component: {fileID: 2669852309572257496}
  m_Layer: 5
  m_Name: MainBG
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &837379061992744721
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 281055667473413853}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: -1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5262535005294521540}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 1.3499, y: -1.0000076}
  m_SizeDelta: {x: 5.3002, y: 10}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &5451908227930356755
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 281055667473413853}
  m_CullTransparentMesh: 1
--- !u!114 &2669852309572257496
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 281055667473413853}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.15686275, g: 0.21176472, b: 0.2784314, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: 1135918bd1512474b9ffe64850dcbea7, type: 3}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 0.85
--- !u!1 &381032404143128124
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3330387465047365296}
  - component: {fileID: 5915796111325714841}
  - component: {fileID: 4817809125322932066}
  - component: {fileID: 4398286667556074562}
  m_Layer: 5
  m_Name: ProgressText
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &3330387465047365296
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 381032404143128124}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 6798695155388857269}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 10, y: 1}
  m_SizeDelta: {x: 20, y: 45}
  m_Pivot: {x: 0.5, y: 1}
--- !u!222 &5915796111325714841
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 381032404143128124}
  m_CullTransparentMesh: 0
--- !u!114 &4817809125322932066
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 381032404143128124}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: 1/15
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: e9fcd18707649e84790f7d932975e8cd, type: 2}
  m_sharedMaterial: {fileID: 2100000, guid: 56dfda18eef0b3a408a511683bfda787, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 1
  m_colorMode: 2
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 0.990566, g: 0.8890435, b: 0.60275006, a: 1}
    bottomRight: {r: 0.990566, g: 0.8890435, b: 0.60275006, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 28
  m_fontSizeBase: 32.29
  m_fontWeight: 400
  m_enableAutoSizing: 1
  m_fontSizeMin: 18
  m_fontSizeMax: 28
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: -30
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 1
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!114 &4398286667556074562
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 381032404143128124}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cc74d835ac42845df8ea6b46c1f44f3b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _textId: CHALLENGE_TRIVIA_INFO_DESCRIPTION
  _breakLines: 1
  _secondaryTexts: []
  _resizeFont: 0
--- !u!1 &453050194727911150
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6980048128946032832}
  m_Layer: 5
  m_Name: HeaderPanel
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &6980048128946032832
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 453050194727911150}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6768226130830604771}
  - {fileID: 1634529854230486656}
  m_Father: {fileID: 5929562591517350087}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 95}
  m_Pivot: {x: 0.5, y: 1}
--- !u!1 &996638913000839317
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8572746615739997797}
  - component: {fileID: 5173532631892346712}
  - component: {fileID: 1664573344540795012}
  m_Layer: 5
  m_Name: Content
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &8572746615739997797
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 996638913000839317}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 1
  m_Children:
  - {fileID: 5909418650870818176}
  - {fileID: 1350373590386955715}
  - {fileID: 8499338271717474507}
  m_Father: {fileID: 4153158389003415927}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0.00012207031}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0, y: 1}
--- !u!114 &5173532631892346712
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 996638913000839317}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 59f8146938fff824cb5fd77236b75775, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Padding:
    m_Left: 0
    m_Right: 0
    m_Top: 10
    m_Bottom: 10
  m_ChildAlignment: 4
  m_Spacing: 5
  m_ChildForceExpandWidth: 0
  m_ChildForceExpandHeight: 0
  m_ChildControlWidth: 0
  m_ChildControlHeight: 0
  m_ChildScaleWidth: 0
  m_ChildScaleHeight: 0
  m_ReverseArrangement: 0
--- !u!114 &1664573344540795012
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 996638913000839317}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3245ec927659c4140ac4f8d17403cc18, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_HorizontalFit: 0
  m_VerticalFit: 2
--- !u!1 &1154796394066750947
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6173342805417950120}
  - component: {fileID: 9135130869634298362}
  - component: {fileID: 3613779094537145331}
  - component: {fileID: 7642976627604638194}
  - component: {fileID: 1904004874756211277}
  m_Layer: 5
  m_Name: Suggested
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &6173342805417950120
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1154796394066750947}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4975385916393566175}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 510, y: 50}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &9135130869634298362
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1154796394066750947}
  m_CullTransparentMesh: 1
--- !u!114 &3613779094537145331
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1154796394066750947}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: Suggested
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: e9fcd18707649e84790f7d932975e8cd, type: 2}
  m_sharedMaterial: {fileID: 2100000, guid: 119947acf130578418347521e0f1c21a, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4284994303
  m_fontColor: {r: 1, g: 0.8235294, b: 0.40392157, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 2
  m_fontColorGradient:
    topLeft: {r: 0.98823535, g: 0.9725491, b: 0.82745105, a: 1}
    topRight: {r: 0.98823535, g: 0.9725491, b: 0.82745105, a: 1}
    bottomLeft: {r: 0.854902, g: 0.654902, b: 0.4156863, a: 1}
    bottomRight: {r: 0.854902, g: 0.654902, b: 0.4156863, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 30
  m_fontSizeBase: 80
  m_fontWeight: 400
  m_enableAutoSizing: 1
  m_fontSizeMin: 18
  m_fontSizeMax: 30
  m_fontStyle: 0
  m_HorizontalAlignment: 1
  m_VerticalAlignment: 1024
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: -50
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 0
  m_wordWrappingRatios: 0.4
  m_overflowMode: 1
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 15, y: 0, z: 15, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!114 &7642976627604638194
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1154796394066750947}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cc74d835ac42845df8ea6b46c1f44f3b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _textId: CHALLENGES_SUGGESTED_TITLE
  _breakLines: 0
  _secondaryTexts: []
  _resizeFont: 0
--- !u!114 &1904004874756211277
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1154796394066750947}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 597fa4b5a640f4f18af64afd8cea1598, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _raycast: 1
  _maskable: 1
  _suppressFont: 0
  _suppressMaterial: 0
  _suppressColorAndGradient: 1
  _suppressFontSize: 1
  _suppressSpacing: 0
  _suppressAlignment: 1
  _suppressOverflow: 0
  _suppressMargins: 1
  SelectedPresetName: ModalTitleRegular_TextMeshProStylePreset
  SelectedLayoutPresetName: ModalTitleRegular_TextMeshProLayoutPreset
--- !u!1 &1179461452442970556
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3674206149143901974}
  m_Layer: 0
  m_Name: LoadingHolder
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!224 &3674206149143901974
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1179461452442970556}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 1
  m_Children:
  - {fileID: 1076015760758255598}
  m_Father: {fileID: 1231443325927710792}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &1629453353375356286
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4870956476606035865}
  - component: {fileID: 6469865979114361014}
  - component: {fileID: 5622089680733412321}
  - component: {fileID: 6127107180378439457}
  m_Layer: 5
  m_Name: BG
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &4870956476606035865
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1629453353375356286}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8418551130642013644}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &6469865979114361014
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1629453353375356286}
  m_CullTransparentMesh: 1
--- !u!114 &5622089680733412321
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1629453353375356286}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.10980393, g: 0.3254902, b: 0.6392157, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!114 &6127107180378439457
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1629453353375356286}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e19747de3f5aca642ab2be37e372fb86, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_EffectColor: {r: 0, g: 0, b: 0, a: 1}
  m_EffectDistance: {x: 0, y: -4}
  m_UseGraphicAlpha: 1
--- !u!1 &1675409318404538199
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4975385916393566175}
  - component: {fileID: 4735268714576681384}
  - component: {fileID: 1517129876783487164}
  m_Layer: 5
  m_Name: Content
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &4975385916393566175
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1675409318404538199}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 1
  m_Children:
  - {fileID: 7931134803991202040}
  - {fileID: 6173342805417950120}
  - {fileID: 7799263473875006413}
  - {fileID: 4381911986259454688}
  - {fileID: 392753702811445562}
  - {fileID: 2431003304041862795}
  - {fileID: 4381191223205497611}
  m_Father: {fileID: 2596595320656012140}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0.00012207031}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0, y: 1}
--- !u!114 &4735268714576681384
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1675409318404538199}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 59f8146938fff824cb5fd77236b75775, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Padding:
    m_Left: 0
    m_Right: 0
    m_Top: 10
    m_Bottom: 10
  m_ChildAlignment: 4
  m_Spacing: 5
  m_ChildForceExpandWidth: 0
  m_ChildForceExpandHeight: 0
  m_ChildControlWidth: 0
  m_ChildControlHeight: 0
  m_ChildScaleWidth: 0
  m_ChildScaleHeight: 0
  m_ReverseArrangement: 0
--- !u!114 &1517129876783487164
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1675409318404538199}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3245ec927659c4140ac4f8d17403cc18, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_HorizontalFit: 0
  m_VerticalFit: 2
--- !u!1 &1704840424077620171
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5909418650870818176}
  - component: {fileID: 8748496567337398226}
  - component: {fileID: 3857577818296800731}
  - component: {fileID: 7908084384658593754}
  - component: {fileID: 8556962708112591172}
  m_Layer: 5
  m_Name: FavoritesTitle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &5909418650870818176
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1704840424077620171}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8572746615739997797}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 510, y: 50}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &8748496567337398226
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1704840424077620171}
  m_CullTransparentMesh: 1
--- !u!114 &3857577818296800731
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1704840424077620171}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: Favorites
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: e9fcd18707649e84790f7d932975e8cd, type: 2}
  m_sharedMaterial: {fileID: 2100000, guid: 119947acf130578418347521e0f1c21a, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4284994303
  m_fontColor: {r: 1, g: 0.8235294, b: 0.40392157, a: 1}
  m_enableVertexGradient: 1
  m_colorMode: 2
  m_fontColorGradient:
    topLeft: {r: 0.98823535, g: 0.9725491, b: 0.82745105, a: 1}
    topRight: {r: 0.98823535, g: 0.9725491, b: 0.82745105, a: 1}
    bottomLeft: {r: 0.854902, g: 0.654902, b: 0.4156863, a: 1}
    bottomRight: {r: 0.854902, g: 0.654902, b: 0.4156863, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 30
  m_fontSizeBase: 80
  m_fontWeight: 400
  m_enableAutoSizing: 1
  m_fontSizeMin: 18
  m_fontSizeMax: 30
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 1024
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: -50
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 0
  m_wordWrappingRatios: 0.4
  m_overflowMode: 1
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 15, y: 0, z: 15, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!114 &7908084384658593754
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1704840424077620171}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cc74d835ac42845df8ea6b46c1f44f3b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _textId: CHALLENGES_FAVORITES_TITLE
  _breakLines: 0
  _secondaryTexts: []
  _resizeFont: 0
--- !u!114 &8556962708112591172
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1704840424077620171}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 597fa4b5a640f4f18af64afd8cea1598, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _raycast: 1
  _maskable: 1
  _suppressFont: 0
  _suppressMaterial: 0
  _suppressColorAndGradient: 1
  _suppressFontSize: 1
  _suppressSpacing: 0
  _suppressAlignment: 1
  _suppressOverflow: 0
  _suppressMargins: 1
  SelectedPresetName: ModalTitleRegular_TextMeshProStylePreset
  SelectedLayoutPresetName: ModalTitleRegular_TextMeshProLayoutPreset
--- !u!1 &1888138808426415154
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3409558869664843675}
  - component: {fileID: 686834357550163554}
  - component: {fileID: 3118008880862557538}
  m_Layer: 5
  m_Name: BG
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &3409558869664843675
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1888138808426415154}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1231443325927710792}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &686834357550163554
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1888138808426415154}
  m_CullTransparentMesh: 1
--- !u!114 &3118008880862557538
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1888138808426415154}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.23137257, g: 0.5254902, b: 0.83921576, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!1 &1931385570457615657
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8418551130642013644}
  m_Layer: 5
  m_Name: HeaderPanel
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &8418551130642013644
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1931385570457615657}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4870956476606035865}
  - {fileID: 4664879964768808569}
  m_Father: {fileID: 5709950868351999918}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 95}
  m_Pivot: {x: 0.5, y: 1}
--- !u!1 &2232449996626335928
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4664879964768808569}
  - component: {fileID: 564515371372981799}
  - component: {fileID: 7181092125335135575}
  - component: {fileID: 502009741490583516}
  - component: {fileID: 1829793586717215712}
  m_Layer: 5
  m_Name: Title
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &4664879964768808569
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2232449996626335928}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8418551130642013644}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: -1.5000011}
  m_SizeDelta: {x: -40, y: -3.0000021}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &564515371372981799
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2232449996626335928}
  m_CullTransparentMesh: 1
--- !u!114 &7181092125335135575
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2232449996626335928}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: Challenge another player to start a new Trivia!
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: e9fcd18707649e84790f7d932975e8cd, type: 2}
  m_sharedMaterial: {fileID: 2100000, guid: 119947acf130578418347521e0f1c21a, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4292277499
  m_fontColor: {r: 0.9843137, g: 0.95686275, b: 0.8392157, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 2
  m_fontColorGradient:
    topLeft: {r: 0.98823535, g: 0.9725491, b: 0.82745105, a: 1}
    topRight: {r: 0.98823535, g: 0.9725491, b: 0.82745105, a: 1}
    bottomLeft: {r: 0.854902, g: 0.654902, b: 0.4156863, a: 1}
    bottomRight: {r: 0.854902, g: 0.654902, b: 0.4156863, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 30
  m_fontSizeBase: 80
  m_fontWeight: 400
  m_enableAutoSizing: 1
  m_fontSizeMin: 18
  m_fontSizeMax: 30
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 4096
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: -50
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 1
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!114 &502009741490583516
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2232449996626335928}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cc74d835ac42845df8ea6b46c1f44f3b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _textId: CHALLENGE_MODAL_NEW_CHALLENGE_TITLE
  _breakLines: 0
  _secondaryTexts: []
  _resizeFont: 0
--- !u!114 &1829793586717215712
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2232449996626335928}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 597fa4b5a640f4f18af64afd8cea1598, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _raycast: 1
  _maskable: 1
  _suppressFont: 0
  _suppressMaterial: 0
  _suppressColorAndGradient: 1
  _suppressFontSize: 1
  _suppressSpacing: 0
  _suppressAlignment: 0
  _suppressOverflow: 0
  _suppressMargins: 0
  SelectedPresetName: ModalTitleRegular_TextMeshProStylePreset
  SelectedLayoutPresetName: ModalTitleRegular_TextMeshProLayoutPreset
--- !u!1 &2309840180579802320
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7390225597057779057}
  - component: {fileID: 6045039379838884217}
  - component: {fileID: 2648241947244732577}
  m_Layer: 5
  m_Name: DeselectedBG
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &7390225597057779057
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2309840180579802320}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1465321419612274322}
  m_Father: {fileID: 6099812445402726806}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 1, y: -0.9999962}
  m_SizeDelta: {x: 2, y: 2}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &6045039379838884217
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2309840180579802320}
  m_CullTransparentMesh: 1
--- !u!114 &2648241947244732577
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2309840180579802320}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0, g: 0.121568635, b: 0.2901961, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: 1135918bd1512474b9ffe64850dcbea7, type: 3}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1.1
--- !u!1 &2355964668199536925
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4642020373182808154}
  - component: {fileID: 7438032127510536276}
  - component: {fileID: 6514325473865167175}
  - component: {fileID: 2558912525217031814}
  m_Layer: 5
  m_Name: DescriptionText
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &4642020373182808154
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2355964668199536925}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 8571093954883246119}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: -3}
  m_SizeDelta: {x: 0, y: 45}
  m_Pivot: {x: 0.5, y: 1}
--- !u!222 &7438032127510536276
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2355964668199536925}
  m_CullTransparentMesh: 0
--- !u!114 &6514325473865167175
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2355964668199536925}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: Send Mr.Wiggles every 15 levels!
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: e9fcd18707649e84790f7d932975e8cd, type: 2}
  m_sharedMaterial: {fileID: 2100000, guid: 56dfda18eef0b3a408a511683bfda787, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 2
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 0.804873, b: 0.24905658, a: 1}
    bottomRight: {r: 1, g: 0.804873, b: 0.24905658, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 28
  m_fontSizeBase: 32.29
  m_fontWeight: 400
  m_enableAutoSizing: 1
  m_fontSizeMin: 18
  m_fontSizeMax: 28
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: -30
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 1
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!114 &2558912525217031814
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2355964668199536925}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cc74d835ac42845df8ea6b46c1f44f3b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _textId: CHALLENGE_TRIVIA_PROGRESS_DESCRIPTION
  _breakLines: 1
  _secondaryTexts: []
  _resizeFont: 0
--- !u!1 &2375896349113375889
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3089483213225846045}
  - component: {fileID: 8092360514127750607}
  - component: {fileID: 348184998453374691}
  m_Layer: 5
  m_Name: Scroll View
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &3089483213225846045
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2375896349113375889}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 855567267964775307}
  - {fileID: 6435351865734981024}
  - {fileID: 2596595320656012140}
  - {fileID: 5775819534805551828}
  m_Father: {fileID: 5929562591517350087}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: -49.500027}
  m_SizeDelta: {x: 0, y: -98.999954}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &8092360514127750607
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2375896349113375889}
  m_CullTransparentMesh: 1
--- !u!114 &348184998453374691
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2375896349113375889}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1aa08ab6e0800fa44ae55d278d1423e3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Content: {fileID: 4975385916393566175}
  m_Horizontal: 0
  m_Vertical: 1
  m_MovementType: 1
  m_Elasticity: 0.1
  m_Inertia: 1
  m_DecelerationRate: 0.135
  m_ScrollSensitivity: 30
  m_Viewport: {fileID: 2596595320656012140}
  m_HorizontalScrollbar: {fileID: 0}
  m_VerticalScrollbar: {fileID: 0}
  m_HorizontalScrollbarVisibility: 2
  m_VerticalScrollbarVisibility: 2
  m_HorizontalScrollbarSpacing: -3
  m_VerticalScrollbarSpacing: -3
  m_OnValueChanged:
    m_PersistentCalls:
      m_Calls: []
--- !u!1 &2516489683733881251
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3182571624525991704}
  - component: {fileID: 1170961249769575711}
  m_Layer: 5
  m_Name: InverseCanvasScaler
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &3182571624525991704
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2516489683733881251}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.85585886, y: 0.85585886, z: 0.85585886}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 614659172238753362}
  - {fileID: 4310905416293676038}
  m_Father: {fileID: 6737153281501167778}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 873.72675, y: 1136}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1170961249769575711
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2516489683733881251}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c517c8e2060dc341a5d2a5fc726cd79, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _wrongReferenceResolution: {x: 640, y: 1136}
  _wrongMatchWidthOrHeight: 0.5
  _referenceResolution: {x: 640, y: 1136}
  _matchWidthOrHeight: 1
  _useSafeAreaExpanding: 0
  _useAspectRatioBasedCurve: 1
  _matchWidthOrHeightAspectRatioBased:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.4615
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 0.4
      value: 0.4615
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 0.5625
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 2
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!1 &2533794974379985441
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6099812445402726806}
  - component: {fileID: 4156948978451378631}
  - component: {fileID: 2979673992708323641}
  - component: {fileID: 2543304480882451988}
  - component: {fileID: 3240936629530573478}
  m_Layer: 5
  m_Name: ActiveChallengesTabButton
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &6099812445402726806
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2533794974379985441}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8493804824271714284}
  - {fileID: 7390225597057779057}
  - {fileID: 7021039355582748800}
  - {fileID: 2341833428726537399}
  - {fileID: 6899492209816783491}
  m_Father: {fileID: 1634529854230486656}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 137.5, y: -32}
  m_SizeDelta: {x: 255, y: 64}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &4156948978451378631
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2533794974379985441}
  m_CullTransparentMesh: 1
--- !u!114 &2979673992708323641
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2533794974379985441}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e7d5d60348944b12b67e2f192ff73588, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _button: {fileID: 2543304480882451988}
  _selectedHolder:
  - {fileID: 2599157000112180534}
  _deselectedHolder:
  - {fileID: 2309840180579802320}
--- !u!114 &2543304480882451988
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2533794974379985441}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4e29b1a8efbd4b44bb3f3716e73f07ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 0
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 7250590168341547356}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &3240936629530573478
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2533794974379985441}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c55beeb4814bb114e9217839046395f3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _soundIdToPlayOnClick: GenericButtonTap
  _haptic: 1
  _impactPreset: 5
--- !u!1 &2599157000112180534
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7021039355582748800}
  - component: {fileID: 8346957724174297890}
  - component: {fileID: 7250590168341547356}
  m_Layer: 5
  m_Name: SelectedBG
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &7021039355582748800
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2599157000112180534}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 133680448028257830}
  m_Father: {fileID: 6099812445402726806}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 1, y: -1}
  m_SizeDelta: {x: 2, y: 1.9999981}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &8346957724174297890
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2599157000112180534}
  m_CullTransparentMesh: 1
--- !u!114 &7250590168341547356
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2599157000112180534}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0, g: 0.121568635, b: 0.2901961, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: 1135918bd1512474b9ffe64850dcbea7, type: 3}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1.1
--- !u!1 &2699129290034222208
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6095267835751828747}
  - component: {fileID: 8970567556651135793}
  - component: {fileID: 5101481975317722452}
  m_Layer: 5
  m_Name: SelectedBG
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &6095267835751828747
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2699129290034222208}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: -1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6347327441932060334}
  m_Father: {fileID: 5262535005294521540}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: -0.6501312, y: -1}
  m_SizeDelta: {x: 1.3002, y: 2}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &8970567556651135793
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2699129290034222208}
  m_CullTransparentMesh: 1
--- !u!114 &5101481975317722452
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2699129290034222208}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.058823533, g: 0.21568629, b: 0.5019608, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: 1135918bd1512474b9ffe64850dcbea7, type: 3}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1.1
--- !u!1 &2703524024178743582
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9101073409767901634}
  - component: {fileID: 7834502015709604595}
  - component: {fileID: 1458161240741626032}
  m_Layer: 5
  m_Name: HeaderImage
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &9101073409767901634
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2703524024178743582}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7300642188881933802}
  m_Father: {fileID: 849465541694258808}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: -22}
  m_SizeDelta: {x: 0, y: -18}
  m_Pivot: {x: 0.5, y: 1}
--- !u!222 &7834502015709604595
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2703524024178743582}
  m_CullTransparentMesh: 1
--- !u!114 &1458161240741626032
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2703524024178743582}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: 852e83d8bba974a4ea2bc22ab46b92a1, type: 3}
  m_Type: 0
  m_PreserveAspect: 1
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!1 &2706840127635535491
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8171024835785730719}
  - component: {fileID: 8309438774238967629}
  - component: {fileID: 7971086816122470820}
  m_Layer: 5
  m_Name: FillingImage
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &8171024835785730719
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2706840127635535491}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6009486342993075155}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: -6}
  m_Pivot: {x: 0, y: 0.5}
--- !u!222 &8309438774238967629
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2706840127635535491}
  m_CullTransparentMesh: 1
--- !u!114 &7971086816122470820
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2706840127635535491}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.62987447, g: 1, b: 0.6273585, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: 17c3c4831f70142f2af97f5168cebd61, type: 3}
  m_Type: 3
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 0
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!1 &2784164382681595768
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4296161333995025605}
  - component: {fileID: 2171837879471451016}
  - component: {fileID: 2360238887443031170}
  - component: {fileID: 6224017399095278070}
  m_Layer: 5
  m_Name: InfoButton
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &4296161333995025605
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2784164382681595768}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 849465541694258808}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 39.7, y: -64.8}
  m_SizeDelta: {x: 45, y: 45}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &2171837879471451016
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2784164382681595768}
  m_CullTransparentMesh: 1
--- !u!114 &2360238887443031170
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2784164382681595768}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: -10, y: -10, z: -10, w: -10}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: 6ecd8542dc8d66846a0a85d0a0fedb6f, type: 3}
  m_Type: 0
  m_PreserveAspect: 1
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!114 &6224017399095278070
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2784164382681595768}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4e29b1a8efbd4b44bb3f3716e73f07ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 2360238887443031170}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
--- !u!1 &2910938857212946011
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1575251893176059226}
  - component: {fileID: 3548553650755943228}
  - component: {fileID: 9063994941859492374}
  - component: {fileID: 2746496907879667762}
  m_Layer: 5
  m_Name: DeselectedBGBase
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1575251893176059226
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2910938857212946011}
  m_LocalRotation: {x: -0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3426846936298758478}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: -1, y: 1.5}
  m_SizeDelta: {x: -2, y: -3}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &3548553650755943228
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2910938857212946011}
  m_CullTransparentMesh: 1
--- !u!114 &9063994941859492374
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2910938857212946011}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: 1135918bd1512474b9ffe64850dcbea7, type: 3}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1.1
--- !u!114 &2746496907879667762
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2910938857212946011}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 45facfdc6a639f041b007c036dc527b8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _gradientType: 1
  _blendMode: 2
  _offset: 0
  _effectGradient:
    serializedVersion: 2
    key0: {r: 0.15686275, g: 0.28235295, b: 0.5254902, a: 1}
    key1: {r: 0.20392159, g: 0.3647059, b: 0.6745098, a: 1}
    key2: {r: 0, g: 0, b: 0, a: 0}
    key3: {r: 0, g: 0, b: 0, a: 0}
    key4: {r: 0, g: 0, b: 0, a: 0}
    key5: {r: 0, g: 0, b: 0, a: 0}
    key6: {r: 0, g: 0, b: 0, a: 0}
    key7: {r: 0, g: 0, b: 0, a: 0}
    ctime0: 0
    ctime1: 65535
    ctime2: 0
    ctime3: 0
    ctime4: 0
    ctime5: 0
    ctime6: 0
    ctime7: 0
    atime0: 0
    atime1: 65535
    atime2: 0
    atime3: 0
    atime4: 0
    atime5: 0
    atime6: 0
    atime7: 0
    m_Mode: 0
    m_ColorSpace: 0
    m_NumColorKeys: 2
    m_NumAlphaKeys: 2
--- !u!1 &2970767574603537607
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7113921931529501645}
  - component: {fileID: 4874853298567659944}
  - component: {fileID: 1621549873568601418}
  m_Layer: 5
  m_Name: Scroll View
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &7113921931529501645
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2970767574603537607}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4153158389003415927}
  m_Father: {fileID: 5709950868351999918}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: -49.500027}
  m_SizeDelta: {x: 0, y: -98.999954}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &4874853298567659944
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2970767574603537607}
  m_CullTransparentMesh: 1
--- !u!114 &1621549873568601418
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2970767574603537607}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1aa08ab6e0800fa44ae55d278d1423e3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Content: {fileID: 8572746615739997797}
  m_Horizontal: 0
  m_Vertical: 1
  m_MovementType: 1
  m_Elasticity: 0.1
  m_Inertia: 1
  m_DecelerationRate: 0.135
  m_ScrollSensitivity: 30
  m_Viewport: {fileID: 4153158389003415927}
  m_HorizontalScrollbar: {fileID: 0}
  m_VerticalScrollbar: {fileID: 0}
  m_HorizontalScrollbarVisibility: 2
  m_VerticalScrollbarVisibility: 2
  m_HorizontalScrollbarSpacing: -3
  m_VerticalScrollbarSpacing: -3
  m_OnValueChanged:
    m_PersistentCalls:
      m_Calls: []
--- !u!1 &3151317646671371955
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7931134803991202040}
  - component: {fileID: 5006756659079774890}
  - component: {fileID: 692335496035760291}
  - component: {fileID: 5885181308484237986}
  - component: {fileID: 8222951625709311069}
  m_Layer: 5
  m_Name: FavoritesTitle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &7931134803991202040
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3151317646671371955}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4975385916393566175}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 510, y: 50}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &5006756659079774890
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3151317646671371955}
  m_CullTransparentMesh: 1
--- !u!114 &692335496035760291
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3151317646671371955}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: Favorites
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: e9fcd18707649e84790f7d932975e8cd, type: 2}
  m_sharedMaterial: {fileID: 2100000, guid: 119947acf130578418347521e0f1c21a, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4284994303
  m_fontColor: {r: 1, g: 0.8235294, b: 0.40392157, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 2
  m_fontColorGradient:
    topLeft: {r: 0.98823535, g: 0.9725491, b: 0.82745105, a: 1}
    topRight: {r: 0.98823535, g: 0.9725491, b: 0.82745105, a: 1}
    bottomLeft: {r: 0.854902, g: 0.654902, b: 0.4156863, a: 1}
    bottomRight: {r: 0.854902, g: 0.654902, b: 0.4156863, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 30
  m_fontSizeBase: 80
  m_fontWeight: 400
  m_enableAutoSizing: 1
  m_fontSizeMin: 18
  m_fontSizeMax: 30
  m_fontStyle: 0
  m_HorizontalAlignment: 1
  m_VerticalAlignment: 1024
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: -50
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 0
  m_wordWrappingRatios: 0.4
  m_overflowMode: 1
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 15, y: 0, z: 15, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!114 &5885181308484237986
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3151317646671371955}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cc74d835ac42845df8ea6b46c1f44f3b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _textId: CHALLENGES_FAVORITES_TITLE
  _breakLines: 0
  _secondaryTexts: []
  _resizeFont: 0
--- !u!114 &8222951625709311069
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3151317646671371955}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 597fa4b5a640f4f18af64afd8cea1598, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _raycast: 1
  _maskable: 1
  _suppressFont: 0
  _suppressMaterial: 0
  _suppressColorAndGradient: 1
  _suppressFontSize: 1
  _suppressSpacing: 0
  _suppressAlignment: 1
  _suppressOverflow: 0
  _suppressMargins: 1
  SelectedPresetName: ModalTitleRegular_TextMeshProStylePreset
  SelectedLayoutPresetName: ModalTitleRegular_TextMeshProLayoutPreset
--- !u!1 &3173323628452419170
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4566207692466893759}
  - component: {fileID: 8865239541100695776}
  - component: {fileID: 5544562915907683442}
  m_Layer: 0
  m_Name: New SkeletonGraphic
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &4566207692466893759
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3173323628452419170}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.5, y: 0.5, z: 0.5}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 7300642188881933802}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 10, y: 4}
  m_SizeDelta: {x: 100, y: 100}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &8865239541100695776
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3173323628452419170}
  m_CullTransparentMesh: 0
--- !u!114 &5544562915907683442
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3173323628452419170}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d85b887af7e6c3f45a2e2d2920d641bc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 2100000, guid: b66cf7a186d13054989b33a5c90044e4, type: 2}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  skeletonDataAsset: {fileID: 11400000, guid: 4137ba3774c2cd04ab2260820bd6ffc1, type: 2}
  additiveMaterial: {fileID: 0}
  multiplyMaterial: {fileID: 0}
  screenMaterial: {fileID: 0}
  m_SkeletonColor: {r: 1, g: 1, b: 1, a: 1}
  initialSkinName: 
  initialFlipX: 0
  initialFlipY: 0
  startingAnimation: Mr. Wiggles challenge - idle
  startingLoop: 1
  timeScale: 1
  freeze: 0
  layoutScaleMode: 0
  referenceSize: {x: 100, y: 100}
  pivotOffset: {x: 0, y: 0}
  referenceScale: 1
  rectTransformSize: {x: 100, y: 100}
  editReferenceRect: 0
  updateWhenInvisible: 3
  allowMultipleCanvasRenderers: 0
  canvasRenderers: []
  separatorSlotNames: []
  enableSeparatorSlots: 0
  separatorParts: []
  updateSeparatorPartLocation: 1
  updateSeparatorPartScale: 0
  disableMeshAssignmentOnOverride: 1
  physicsPositionInheritanceFactor: {x: 1, y: 1}
  physicsRotationInheritanceFactor: 1
  physicsMovementRelativeTo: {fileID: 0}
  meshGenerator:
    settings:
      useClipping: 1
      zSpacing: 0
      tintBlack: 0
      canvasGroupCompatible: 0
      pmaVertexColors: 1
      addNormals: 0
      calculateTangents: 0
      immutableTriangles: 0
  updateTiming: 1
  unscaledTime: 0
--- !u!1 &3290071762581459227
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6435351865734981024}
  - component: {fileID: 7021433224213127084}
  - component: {fileID: 6452007576666519232}
  m_Layer: 5
  m_Name: PatternOverlay
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &6435351865734981024
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3290071762581459227}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3089483213225846045}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &7021433224213127084
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3290071762581459227}
  m_CullTransparentMesh: 1
--- !u!114 &6452007576666519232
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3290071762581459227}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 0.6117647}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: 8c882f007563ffe4887b777ef4a5eec7, type: 3}
  m_Type: 2
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1.3
--- !u!1 &3427510509589986544
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3706680152742352446}
  - component: {fileID: 6971339505994316867}
  - component: {fileID: 8798352746853412206}
  m_Layer: 5
  m_Name: BarBGOutlineShadow
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &3706680152742352446
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3427510509589986544}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6798695155388857269}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 2, y: 2}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &6971339505994316867
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3427510509589986544}
  m_CullTransparentMesh: 1
--- !u!114 &8798352746853412206
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3427510509589986544}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 0.9098351, b: 0.5613208, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: 473ab620fdf19420c938e9e83b5c13bb, type: 3}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 7
--- !u!1 &3560250662791634631
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3426846936298758478}
  - component: {fileID: 1498669540228780345}
  - component: {fileID: 8503922180505992437}
  m_Layer: 5
  m_Name: DeselectedBG
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &3426846936298758478
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3560250662791634631}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: -1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1575251893176059226}
  m_Father: {fileID: 5262535005294521540}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: -0.6501312, y: -1}
  m_SizeDelta: {x: 1.3002, y: 2}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &1498669540228780345
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3560250662791634631}
  m_CullTransparentMesh: 1
--- !u!114 &8503922180505992437
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3560250662791634631}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0, g: 0.121568635, b: 0.2901961, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: 1135918bd1512474b9ffe64850dcbea7, type: 3}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1.1
--- !u!1 &3736245568910627166
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4153158389003415927}
  - component: {fileID: 1559202438661412048}
  - component: {fileID: 5694517975298985395}
  m_Layer: 5
  m_Name: Viewport
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &4153158389003415927
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3736245568910627166}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8572746615739997797}
  m_Father: {fileID: 7113921931529501645}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0, y: 1}
--- !u!222 &1559202438661412048
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3736245568910627166}
  m_CullTransparentMesh: 1
--- !u!114 &5694517975298985395
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3736245568910627166}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3312d7739989d2b4e91e6319e9a96d76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Padding: {x: 0, y: 0, z: 0, w: 0}
  m_Softness: {x: 0, y: 0}
--- !u!1 &3824613319224366770
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5556645877921939268}
  - component: {fileID: 8869922395082092475}
  - component: {fileID: 5494650516814293915}
  - component: {fileID: 4781570085139216623}
  m_Layer: 5
  m_Name: LoadingText
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &5556645877921939268
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3824613319224366770}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1231443325927710792}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0.5}
  m_AnchorMax: {x: 1, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 30}
  m_SizeDelta: {x: -50, y: 100}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &8869922395082092475
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3824613319224366770}
  m_CullTransparentMesh: 1
--- !u!114 &5494650516814293915
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3824613319224366770}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: 
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: e9fcd18707649e84790f7d932975e8cd, type: 2}
  m_sharedMaterial: {fileID: 2100000, guid: 119947acf130578418347521e0f1c21a, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4292277500
  m_fontColor: {r: 0.98823535, g: 0.9568628, b: 0.83921576, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 2
  m_fontColorGradient:
    topLeft: {r: 0.98823535, g: 0.9725491, b: 0.82745105, a: 1}
    topRight: {r: 0.98823535, g: 0.9725491, b: 0.82745105, a: 1}
    bottomLeft: {r: 0.854902, g: 0.654902, b: 0.4156863, a: 1}
    bottomRight: {r: 0.854902, g: 0.654902, b: 0.4156863, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 32
  m_fontSizeBase: 80
  m_fontWeight: 400
  m_enableAutoSizing: 1
  m_fontSizeMin: 18
  m_fontSizeMax: 32
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: -50
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 1
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!114 &4781570085139216623
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3824613319224366770}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cc74d835ac42845df8ea6b46c1f44f3b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _textId: CHALLENGE_LOADING
  _breakLines: 0
  _secondaryTexts: []
  _resizeFont: 0
--- !u!1 &3931194131015773049
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6009486342993075155}
  - component: {fileID: 118128011814162403}
  - component: {fileID: 8823227646064151829}
  - component: {fileID: 7806517292035851171}
  m_Layer: 5
  m_Name: Mask
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &6009486342993075155
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3931194131015773049}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8171024835785730719}
  m_Father: {fileID: 6798695155388857269}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &118128011814162403
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3931194131015773049}
  m_CullTransparentMesh: 1
--- !u!114 &8823227646064151829
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3931194131015773049}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: 1b4e1d30c7f411c4fb071019fecb1ba9, type: 3}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 2
--- !u!114 &7806517292035851171
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3931194131015773049}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ********************************, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_ShowMaskGraphic: 0
--- !u!1 &3964436927969916676
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 127589320422069527}
  - component: {fileID: 855855318030578639}
  - component: {fileID: 4141019705890782703}
  - component: {fileID: 1023312878479035046}
  - component: {fileID: 4105937389097996610}
  m_Layer: 5
  m_Name: Title
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &127589320422069527
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3964436927969916676}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5262535005294521540}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: -30, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &855855318030578639
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3964436927969916676}
  m_CullTransparentMesh: 1
--- !u!114 &4141019705890782703
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3964436927969916676}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: Favorites
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: e9fcd18707649e84790f7d932975e8cd, type: 2}
  m_sharedMaterial: {fileID: 2100000, guid: 119947acf130578418347521e0f1c21a, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4292277500
  m_fontColor: {r: 0.9882353, g: 0.95686275, b: 0.8392157, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 2
  m_fontColorGradient:
    topLeft: {r: 0.98823535, g: 0.9725491, b: 0.82745105, a: 1}
    topRight: {r: 0.98823535, g: 0.9725491, b: 0.82745105, a: 1}
    bottomLeft: {r: 0.854902, g: 0.654902, b: 0.4156863, a: 1}
    bottomRight: {r: 0.854902, g: 0.654902, b: 0.4156863, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 32
  m_fontSizeBase: 80
  m_fontWeight: 400
  m_enableAutoSizing: 1
  m_fontSizeMin: 18
  m_fontSizeMax: 32
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: -50
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 1
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!114 &1023312878479035046
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3964436927969916676}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cc74d835ac42845df8ea6b46c1f44f3b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _textId: CHALLENGE_MODAL_FAVORITES_TITLE
  _breakLines: 0
  _secondaryTexts: []
  _resizeFont: 0
--- !u!114 &4105937389097996610
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3964436927969916676}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 597fa4b5a640f4f18af64afd8cea1598, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _raycast: 1
  _maskable: 1
  _suppressFont: 0
  _suppressMaterial: 0
  _suppressColorAndGradient: 1
  _suppressFontSize: 1
  _suppressSpacing: 0
  _suppressAlignment: 1
  _suppressOverflow: 0
  _suppressMargins: 0
  SelectedPresetName: ModalTitleRegular_TextMeshProStylePreset
  SelectedLayoutPresetName: ModalTitleRegular_TextMeshProLayoutPreset
--- !u!1 &4141970975613589075
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4381191223205497611}
  - component: {fileID: 1785649593993638941}
  - component: {fileID: 1956602376399228482}
  - component: {fileID: 873923464509877767}
  m_Layer: 5
  m_Name: Expired
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &4381191223205497611
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4141970975613589075}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4975385916393566175}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 510, y: 50}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &1785649593993638941
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4141970975613589075}
  m_CullTransparentMesh: 1
--- !u!114 &1956602376399228482
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4141970975613589075}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: Expired
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: e9fcd18707649e84790f7d932975e8cd, type: 2}
  m_sharedMaterial: {fileID: 2100000, guid: 119947acf130578418347521e0f1c21a, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4284994303
  m_fontColor: {r: 1, g: 0.8235295, b: 0.4039216, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 2
  m_fontColorGradient:
    topLeft: {r: 0.98823535, g: 0.9725491, b: 0.82745105, a: 1}
    topRight: {r: 0.98823535, g: 0.9725491, b: 0.82745105, a: 1}
    bottomLeft: {r: 0.854902, g: 0.654902, b: 0.4156863, a: 1}
    bottomRight: {r: 0.854902, g: 0.654902, b: 0.4156863, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 30
  m_fontSizeBase: 80
  m_fontWeight: 400
  m_enableAutoSizing: 1
  m_fontSizeMin: 18
  m_fontSizeMax: 30
  m_fontStyle: 0
  m_HorizontalAlignment: 1
  m_VerticalAlignment: 1024
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: -50
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 0
  m_wordWrappingRatios: 0.4
  m_overflowMode: 1
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 15, y: 0, z: 15, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!114 &873923464509877767
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4141970975613589075}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cc74d835ac42845df8ea6b46c1f44f3b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _textId: CHALLENGES_EXPIRED_TITLE
  _breakLines: 0
  _secondaryTexts: []
  _resizeFont: 0
--- !u!1 &4164207827705566374
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8482133881373267648}
  - component: {fileID: 4632694083249566130}
  - component: {fileID: 30960367584989585}
  m_Layer: 5
  m_Name: TopBG
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &8482133881373267648
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4164207827705566374}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 849465541694258808}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: -62}
  m_SizeDelta: {x: 0, y: 71}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &4632694083249566130
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4164207827705566374}
  m_CullTransparentMesh: 1
--- !u!114 &30960367584989585
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4164207827705566374}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.01886791, g: 0.01886791, b: 0.01886791, a: 0.11764706}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!1 &4432553869441972629
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6437538827499459053}
  - component: {fileID: 6990431303964986911}
  - component: {fileID: 539030092350341304}
  m_Layer: 5
  m_Name: BoosterImage
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!224 &6437538827499459053
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4432553869441972629}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6798695155388857269}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 0.5}
  m_AnchorMax: {x: 1, y: 0.5}
  m_AnchoredPosition: {x: -9.8, y: 0}
  m_SizeDelta: {x: 75.224, y: 50}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &6990431303964986911
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4432553869441972629}
  m_CullTransparentMesh: 1
--- !u!114 &539030092350341304
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4432553869441972629}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: 5356da03007e540529bb0af5b0137ed5, type: 3}
  m_Type: 0
  m_PreserveAspect: 1
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!1 &4755476023196636020
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1634529854230486656}
  - component: {fileID: 4550254767481924217}
  m_Layer: 5
  m_Name: TabButtons
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1634529854230486656
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4755476023196636020}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 1
  m_Children:
  - {fileID: 6099812445402726806}
  - {fileID: 5262535005294521540}
  m_Father: {fileID: 6980048128946032832}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 530, y: 64}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &4550254767481924217
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4755476023196636020}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 30649d3a9faa99c48a7b1166b86bf2a0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Padding:
    m_Left: 10
    m_Right: 10
    m_Top: 0
    m_Bottom: 0
  m_ChildAlignment: 4
  m_Spacing: 0
  m_ChildForceExpandWidth: 1
  m_ChildForceExpandHeight: 1
  m_ChildControlWidth: 1
  m_ChildControlHeight: 1
  m_ChildScaleWidth: 0
  m_ChildScaleHeight: 0
  m_ReverseArrangement: 0
--- !u!1 &5167069118984150512
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 14243442040091414}
  - component: {fileID: 2925102193256647500}
  m_Layer: 5
  m_Name: Line
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &14243442040091414
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5167069118984150512}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2653116513856503030}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 2.5, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &2925102193256647500
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5167069118984150512}
  m_CullTransparentMesh: 1
--- !u!1 &5290244867468233276
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7300642188881933802}
  - component: {fileID: 8120797203440086215}
  - component: {fileID: 3535275312764425988}
  - component: {fileID: 8515359068752172514}
  m_Layer: 5
  m_Name: Mask
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &7300642188881933802
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5290244867468233276}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4566207692466893759}
  m_Father: {fileID: 9101073409767901634}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0.000030517578}
  m_SizeDelta: {x: 0, y: -3.9057}
  m_Pivot: {x: 0.5, y: 1}
--- !u!222 &8120797203440086215
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5290244867468233276}
  m_CullTransparentMesh: 1
--- !u!114 &3535275312764425988
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5290244867468233276}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 1
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!114 &8515359068752172514
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5290244867468233276}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ********************************, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_ShowMaskGraphic: 0
--- !u!1 &5687882459558423258
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7799263473875006413}
  - component: {fileID: 1185226662609476848}
  - component: {fileID: 3745426605946950094}
  - component: {fileID: 7898664886651006914}
  - component: {fileID: 6213693587594717862}
  m_Layer: 5
  m_Name: Completed
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &7799263473875006413
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5687882459558423258}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4975385916393566175}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 510, y: 50}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &1185226662609476848
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5687882459558423258}
  m_CullTransparentMesh: 1
--- !u!114 &3745426605946950094
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5687882459558423258}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: Completed
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: e9fcd18707649e84790f7d932975e8cd, type: 2}
  m_sharedMaterial: {fileID: 2100000, guid: 119947acf130578418347521e0f1c21a, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4284994303
  m_fontColor: {r: 1, g: 0.8235294, b: 0.40392157, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 2
  m_fontColorGradient:
    topLeft: {r: 0.98823535, g: 0.9725491, b: 0.82745105, a: 1}
    topRight: {r: 0.98823535, g: 0.9725491, b: 0.82745105, a: 1}
    bottomLeft: {r: 0.854902, g: 0.654902, b: 0.4156863, a: 1}
    bottomRight: {r: 0.854902, g: 0.654902, b: 0.4156863, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 30
  m_fontSizeBase: 80
  m_fontWeight: 400
  m_enableAutoSizing: 1
  m_fontSizeMin: 18
  m_fontSizeMax: 30
  m_fontStyle: 0
  m_HorizontalAlignment: 1
  m_VerticalAlignment: 1024
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: -50
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 0
  m_wordWrappingRatios: 0.4
  m_overflowMode: 1
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 15, y: 0, z: 15, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!114 &7898664886651006914
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5687882459558423258}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cc74d835ac42845df8ea6b46c1f44f3b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _textId: CHALLENGES_COMPLETED_TITLE
  _breakLines: 0
  _secondaryTexts: []
  _resizeFont: 0
--- !u!114 &6213693587594717862
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5687882459558423258}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 597fa4b5a640f4f18af64afd8cea1598, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _raycast: 1
  _maskable: 1
  _suppressFont: 0
  _suppressMaterial: 0
  _suppressColorAndGradient: 1
  _suppressFontSize: 1
  _suppressSpacing: 0
  _suppressAlignment: 1
  _suppressOverflow: 0
  _suppressMargins: 1
  SelectedPresetName: ModalTitleRegular_TextMeshProStylePreset
  SelectedLayoutPresetName: ModalTitleRegular_TextMeshProLayoutPreset
--- !u!1 &5836693647258731066
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1465321419612274322}
  - component: {fileID: 1755543837893874674}
  - component: {fileID: 528529863703768906}
  - component: {fileID: 6546531718101961034}
  m_Layer: 5
  m_Name: DeselectedBGBase
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1465321419612274322
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5836693647258731066}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7390225597057779057}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: -1, y: 1.5}
  m_SizeDelta: {x: -2, y: -3}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &1755543837893874674
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5836693647258731066}
  m_CullTransparentMesh: 1
--- !u!114 &528529863703768906
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5836693647258731066}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: 1135918bd1512474b9ffe64850dcbea7, type: 3}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1.1
--- !u!114 &6546531718101961034
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5836693647258731066}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 45facfdc6a639f041b007c036dc527b8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _gradientType: 1
  _blendMode: 2
  _offset: 0
  _effectGradient:
    serializedVersion: 2
    key0: {r: 0.15686275, g: 0.28235295, b: 0.5254902, a: 1}
    key1: {r: 0.20392159, g: 0.3647059, b: 0.6745098, a: 1}
    key2: {r: 0, g: 0, b: 0, a: 0}
    key3: {r: 0, g: 0, b: 0, a: 0}
    key4: {r: 0, g: 0, b: 0, a: 0}
    key5: {r: 0, g: 0, b: 0, a: 0}
    key6: {r: 0, g: 0, b: 0, a: 0}
    key7: {r: 0, g: 0, b: 0, a: 0}
    ctime0: 0
    ctime1: 65535
    ctime2: 0
    ctime3: 0
    ctime4: 0
    ctime5: 0
    ctime6: 0
    ctime7: 0
    atime0: 0
    atime1: 65535
    atime2: 0
    atime3: 0
    atime4: 0
    atime5: 0
    atime6: 0
    atime7: 0
    m_Mode: 0
    m_ColorSpace: 0
    m_NumColorKeys: 2
    m_NumAlphaKeys: 2
--- !u!1 &5910373845371789088
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 855567267964775307}
  - component: {fileID: 5542058102896624345}
  - component: {fileID: 3800272784266089305}
  m_Layer: 5
  m_Name: BG
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &855567267964775307
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5910373845371789088}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3089483213225846045}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &5542058102896624345
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5910373845371789088}
  m_CullTransparentMesh: 1
--- !u!114 &3800272784266089305
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5910373845371789088}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.23137257, g: 0.5254902, b: 0.83921576, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!1 &5945018211862183136
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6768226130830604771}
  - component: {fileID: 4210253173554518949}
  - component: {fileID: 7601815103076752866}
  - component: {fileID: 571355574767470512}
  m_Layer: 5
  m_Name: BG
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &6768226130830604771
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5945018211862183136}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6980048128946032832}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &4210253173554518949
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5945018211862183136}
  m_CullTransparentMesh: 1
--- !u!114 &7601815103076752866
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5945018211862183136}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.10980393, g: 0.3254902, b: 0.6392157, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!114 &571355574767470512
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5945018211862183136}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e19747de3f5aca642ab2be37e372fb86, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_EffectColor: {r: 0, g: 0, b: 0, a: 1}
  m_EffectDistance: {x: 0, y: -4}
  m_UseGraphicAlpha: 1
--- !u!1 &6168149527932757242
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6798695155388857269}
  - component: {fileID: 1280380112848891675}
  m_Layer: 5
  m_Name: ProgressBar
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &6798695155388857269
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6168149527932757242}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 1
  m_Children:
  - {fileID: 2036569547087806546}
  - {fileID: 6009486342993075155}
  - {fileID: 2653116513856503030}
  - {fileID: 4520299061184886203}
  - {fileID: 3706680152742352446}
  - {fileID: 3330387465047365296}
  - {fileID: 6437538827499459053}
  m_Father: {fileID: 8571093954883246119}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: 0, y: 8}
  m_SizeDelta: {x: 0, y: 42}
  m_Pivot: {x: 0.5, y: 0}
--- !u!114 &1280380112848891675
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6168149527932757242}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3c78b7b2ab08eec4eaf3cc0a389761b8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _fillImage: {fileID: 7971086816122470820}
  _thumbHolderTransform: {fileID: 0}
  _selfTransform: {fileID: 6798695155388857269}
  _progressBarAlignment: 0
  _progressBarHighlightAnchor: {fileID: 0}
--- !u!1 &6236034599229571153
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8571093954883246119}
  m_Layer: 5
  m_Name: Progress
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &8571093954883246119
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6236034599229571153}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6798695155388857269}
  - {fileID: 4642020373182808154}
  m_Father: {fileID: 849465541694258808}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: -60, y: 90}
  m_Pivot: {x: 0.5, y: 0}
--- !u!1 &6275706713034392456
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1350373590386955715}
  - component: {fileID: 4168048550949871505}
  - component: {fileID: 8446461253537080728}
  - component: {fileID: 3242641817372452761}
  - component: {fileID: 4435074337337377650}
  m_Layer: 5
  m_Name: TeamTitle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1350373590386955715
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6275706713034392456}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8572746615739997797}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 510, y: 50}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &4168048550949871505
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6275706713034392456}
  m_CullTransparentMesh: 1
--- !u!114 &8446461253537080728
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6275706713034392456}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: Team
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: e9fcd18707649e84790f7d932975e8cd, type: 2}
  m_sharedMaterial: {fileID: 2100000, guid: 119947acf130578418347521e0f1c21a, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4284994303
  m_fontColor: {r: 1, g: 0.8235294, b: 0.40392157, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 2
  m_fontColorGradient:
    topLeft: {r: 0.98823535, g: 0.9725491, b: 0.82745105, a: 1}
    topRight: {r: 0.98823535, g: 0.9725491, b: 0.82745105, a: 1}
    bottomLeft: {r: 0.854902, g: 0.654902, b: 0.4156863, a: 1}
    bottomRight: {r: 0.854902, g: 0.654902, b: 0.4156863, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 30
  m_fontSizeBase: 80
  m_fontWeight: 400
  m_enableAutoSizing: 1
  m_fontSizeMin: 18
  m_fontSizeMax: 30
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 1024
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: -50
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 0
  m_wordWrappingRatios: 0.4
  m_overflowMode: 1
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 15, y: 0, z: 15, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!114 &3242641817372452761
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6275706713034392456}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cc74d835ac42845df8ea6b46c1f44f3b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _textId: CHALLENGES_TEAM_TITLE
  _breakLines: 0
  _secondaryTexts: []
  _resizeFont: 0
--- !u!114 &4435074337337377650
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6275706713034392456}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 597fa4b5a640f4f18af64afd8cea1598, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _raycast: 1
  _maskable: 1
  _suppressFont: 0
  _suppressMaterial: 0
  _suppressColorAndGradient: 1
  _suppressFontSize: 1
  _suppressSpacing: 0
  _suppressAlignment: 1
  _suppressOverflow: 0
  _suppressMargins: 1
  SelectedPresetName: ModalTitleRegular_TextMeshProStylePreset
  SelectedLayoutPresetName: ModalTitleRegular_TextMeshProLayoutPreset
--- !u!1 &6563432687324345605
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2036569547087806546}
  - component: {fileID: 727075381723399407}
  - component: {fileID: 1874171037668768035}
  m_Layer: 5
  m_Name: BarBG
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2036569547087806546
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6563432687324345605}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6798695155388857269}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0.66329956, y: 0.68362427}
  m_SizeDelta: {x: -4.4022, y: -4.6328}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &727075381723399407
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6563432687324345605}
  m_CullTransparentMesh: 1
--- !u!114 &1874171037668768035
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6563432687324345605}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.043137256, g: 0.16470589, b: 0.29411766, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: 1b4e1d30c7f411c4fb071019fecb1ba9, type: 3}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 7
--- !u!1 &6943254477892184034
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2431003304041862795}
  - component: {fileID: 4250845722460467555}
  - component: {fileID: 7387016285206046073}
  - component: {fileID: 1676073699442196996}
  - component: {fileID: 4125235032437649627}
  m_Layer: 5
  m_Name: TheirTurn
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2431003304041862795
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6943254477892184034}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4975385916393566175}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 510, y: 50}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &4250845722460467555
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6943254477892184034}
  m_CullTransparentMesh: 1
--- !u!114 &7387016285206046073
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6943254477892184034}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: Their Turn
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: e9fcd18707649e84790f7d932975e8cd, type: 2}
  m_sharedMaterial: {fileID: 2100000, guid: 119947acf130578418347521e0f1c21a, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4284994303
  m_fontColor: {r: 1, g: 0.8235294, b: 0.40392157, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 2
  m_fontColorGradient:
    topLeft: {r: 0.98823535, g: 0.9725491, b: 0.82745105, a: 1}
    topRight: {r: 0.98823535, g: 0.9725491, b: 0.82745105, a: 1}
    bottomLeft: {r: 0.854902, g: 0.654902, b: 0.4156863, a: 1}
    bottomRight: {r: 0.854902, g: 0.654902, b: 0.4156863, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 30
  m_fontSizeBase: 80
  m_fontWeight: 400
  m_enableAutoSizing: 1
  m_fontSizeMin: 18
  m_fontSizeMax: 30
  m_fontStyle: 0
  m_HorizontalAlignment: 1
  m_VerticalAlignment: 1024
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: -50
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 0
  m_wordWrappingRatios: 0.4
  m_overflowMode: 1
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 15, y: 0, z: 15, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!114 &1676073699442196996
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6943254477892184034}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cc74d835ac42845df8ea6b46c1f44f3b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _textId: CHALLENGES_THEIR_TURN_TITLE
  _breakLines: 0
  _secondaryTexts: []
  _resizeFont: 0
--- !u!114 &4125235032437649627
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6943254477892184034}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 597fa4b5a640f4f18af64afd8cea1598, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _raycast: 1
  _maskable: 1
  _suppressFont: 0
  _suppressMaterial: 0
  _suppressColorAndGradient: 1
  _suppressFontSize: 1
  _suppressSpacing: 0
  _suppressAlignment: 1
  _suppressOverflow: 0
  _suppressMargins: 1
  SelectedPresetName: ModalTitleRegular_TextMeshProStylePreset
  SelectedLayoutPresetName: ModalTitleRegular_TextMeshProLayoutPreset
--- !u!1 &7326791457717167878
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1231443325927710792}
  m_Layer: 5
  m_Name: ContentHolder
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1231443325927710792
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7326791457717167878}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3409558869664843675}
  - {fileID: 431583278720427933}
  - {fileID: 5556645877921939268}
  - {fileID: 5709950868351999918}
  - {fileID: 5929562591517350087}
  - {fileID: 3674206149143901974}
  m_Father: {fileID: 1150874204090936929}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: -137}
  m_SizeDelta: {x: -60, y: -334}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &7695447736659610262
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5775819534805551828}
  - component: {fileID: 6729480990991982798}
  - component: {fileID: 8660829567727143202}
  - component: {fileID: 1650623575729350438}
  m_Layer: 5
  m_Name: EmptyText
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &5775819534805551828
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7695447736659610262}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3089483213225846045}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0.5}
  m_AnchorMax: {x: 1, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 30}
  m_SizeDelta: {x: -50, y: 100}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &6729480990991982798
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7695447736659610262}
  m_CullTransparentMesh: 1
--- !u!114 &8660829567727143202
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7695447736659610262}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: 
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: e9fcd18707649e84790f7d932975e8cd, type: 2}
  m_sharedMaterial: {fileID: 2100000, guid: 119947acf130578418347521e0f1c21a, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4292277500
  m_fontColor: {r: 0.98823535, g: 0.9568628, b: 0.83921576, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 2
  m_fontColorGradient:
    topLeft: {r: 0.98823535, g: 0.9725491, b: 0.82745105, a: 1}
    topRight: {r: 0.98823535, g: 0.9725491, b: 0.82745105, a: 1}
    bottomLeft: {r: 0.854902, g: 0.654902, b: 0.4156863, a: 1}
    bottomRight: {r: 0.854902, g: 0.654902, b: 0.4156863, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 32
  m_fontSizeBase: 80
  m_fontWeight: 400
  m_enableAutoSizing: 1
  m_fontSizeMin: 18
  m_fontSizeMax: 32
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: -50
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 1
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!114 &1650623575729350438
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7695447736659610262}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cc74d835ac42845df8ea6b46c1f44f3b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _textId: CHALLENGE_EMPTY
  _breakLines: 0
  _secondaryTexts: []
  _resizeFont: 0
--- !u!1 &7721471823033993384
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6899492209816783491}
  - component: {fileID: 3664682970133084933}
  - component: {fileID: 1835010915799266635}
  - component: {fileID: 5590610844391416021}
  - component: {fileID: 4202231827669688945}
  m_Layer: 5
  m_Name: Title
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &6899492209816783491
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7721471823033993384}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6099812445402726806}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: -30, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &3664682970133084933
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7721471823033993384}
  m_CullTransparentMesh: 1
--- !u!114 &1835010915799266635
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7721471823033993384}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: Active Games
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: e9fcd18707649e84790f7d932975e8cd, type: 2}
  m_sharedMaterial: {fileID: 2100000, guid: 119947acf130578418347521e0f1c21a, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4292277500
  m_fontColor: {r: 0.9882353, g: 0.95686275, b: 0.8392157, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 2
  m_fontColorGradient:
    topLeft: {r: 0.98823535, g: 0.9725491, b: 0.82745105, a: 1}
    topRight: {r: 0.98823535, g: 0.9725491, b: 0.82745105, a: 1}
    bottomLeft: {r: 0.854902, g: 0.654902, b: 0.4156863, a: 1}
    bottomRight: {r: 0.854902, g: 0.654902, b: 0.4156863, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 32
  m_fontSizeBase: 80
  m_fontWeight: 400
  m_enableAutoSizing: 1
  m_fontSizeMin: 18
  m_fontSizeMax: 32
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: -50
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 1
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!114 &5590610844391416021
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7721471823033993384}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cc74d835ac42845df8ea6b46c1f44f3b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _textId: CHALLENGE_MODAL_ACTIVE_GAMES_TITLE
  _breakLines: 0
  _secondaryTexts: []
  _resizeFont: 0
--- !u!114 &4202231827669688945
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7721471823033993384}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 597fa4b5a640f4f18af64afd8cea1598, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _raycast: 1
  _maskable: 1
  _suppressFont: 0
  _suppressMaterial: 0
  _suppressColorAndGradient: 1
  _suppressFontSize: 1
  _suppressSpacing: 0
  _suppressAlignment: 1
  _suppressOverflow: 0
  _suppressMargins: 0
  SelectedPresetName: ModalTitleRegular_TextMeshProStylePreset
  SelectedLayoutPresetName: ModalTitleRegular_TextMeshProLayoutPreset
--- !u!1 &7885649954253082596
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2596595320656012140}
  - component: {fileID: 5150943929797859474}
  - component: {fileID: 8847430876053868663}
  m_Layer: 5
  m_Name: Viewport
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2596595320656012140
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7885649954253082596}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4975385916393566175}
  m_Father: {fileID: 3089483213225846045}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0, y: 1}
--- !u!222 &5150943929797859474
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7885649954253082596}
  m_CullTransparentMesh: 1
--- !u!114 &8847430876053868663
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7885649954253082596}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3312d7739989d2b4e91e6319e9a96d76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Padding: {x: 0, y: 0, z: 0, w: 0}
  m_Softness: {x: 0, y: 0}
--- !u!1 &8131529661694877009
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 392753702811445562}
  - component: {fileID: 6619537470169208574}
  - component: {fileID: 4685179930868611377}
  - component: {fileID: 4704135346397719005}
  - component: {fileID: 4731571419915513124}
  m_Layer: 5
  m_Name: YourTurn
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &392753702811445562
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8131529661694877009}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4975385916393566175}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 510, y: 50}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &6619537470169208574
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8131529661694877009}
  m_CullTransparentMesh: 1
--- !u!114 &4685179930868611377
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8131529661694877009}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: Your Turn
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: e9fcd18707649e84790f7d932975e8cd, type: 2}
  m_sharedMaterial: {fileID: 2100000, guid: 119947acf130578418347521e0f1c21a, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4284994303
  m_fontColor: {r: 1, g: 0.8235294, b: 0.40392157, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 2
  m_fontColorGradient:
    topLeft: {r: 0.98823535, g: 0.9725491, b: 0.82745105, a: 1}
    topRight: {r: 0.98823535, g: 0.9725491, b: 0.82745105, a: 1}
    bottomLeft: {r: 0.854902, g: 0.654902, b: 0.4156863, a: 1}
    bottomRight: {r: 0.854902, g: 0.654902, b: 0.4156863, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 30
  m_fontSizeBase: 80
  m_fontWeight: 400
  m_enableAutoSizing: 1
  m_fontSizeMin: 18
  m_fontSizeMax: 30
  m_fontStyle: 0
  m_HorizontalAlignment: 1
  m_VerticalAlignment: 1024
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: -50
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 0
  m_wordWrappingRatios: 0.4
  m_overflowMode: 1
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 15, y: 0, z: 15, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!114 &4704135346397719005
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8131529661694877009}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cc74d835ac42845df8ea6b46c1f44f3b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _textId: CHALLENGES_YOUR_TURN_TITLE
  _breakLines: 0
  _secondaryTexts: []
  _resizeFont: 0
--- !u!114 &4731571419915513124
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8131529661694877009}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 597fa4b5a640f4f18af64afd8cea1598, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _raycast: 1
  _maskable: 1
  _suppressFont: 0
  _suppressMaterial: 0
  _suppressColorAndGradient: 1
  _suppressFontSize: 1
  _suppressSpacing: 0
  _suppressAlignment: 1
  _suppressOverflow: 0
  _suppressMargins: 1
  SelectedPresetName: ModalTitleRegular_TextMeshProStylePreset
  SelectedLayoutPresetName: ModalTitleRegular_TextMeshProLayoutPreset
--- !u!1 &8177616747508084018
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4381911986259454688}
  - component: {fileID: 2789315665754124936}
  - component: {fileID: 1069328449146731999}
  - component: {fileID: 2070740858637139579}
  - component: {fileID: 823836187904062331}
  m_Layer: 5
  m_Name: Failed
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &4381911986259454688
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8177616747508084018}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4975385916393566175}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 510, y: 50}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &2789315665754124936
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8177616747508084018}
  m_CullTransparentMesh: 1
--- !u!114 &1069328449146731999
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8177616747508084018}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: Failed
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: e9fcd18707649e84790f7d932975e8cd, type: 2}
  m_sharedMaterial: {fileID: 2100000, guid: 119947acf130578418347521e0f1c21a, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4284994303
  m_fontColor: {r: 1, g: 0.8235294, b: 0.40392157, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 2
  m_fontColorGradient:
    topLeft: {r: 0.98823535, g: 0.9725491, b: 0.82745105, a: 1}
    topRight: {r: 0.98823535, g: 0.9725491, b: 0.82745105, a: 1}
    bottomLeft: {r: 0.854902, g: 0.654902, b: 0.4156863, a: 1}
    bottomRight: {r: 0.854902, g: 0.654902, b: 0.4156863, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 30
  m_fontSizeBase: 80
  m_fontWeight: 400
  m_enableAutoSizing: 1
  m_fontSizeMin: 18
  m_fontSizeMax: 30
  m_fontStyle: 0
  m_HorizontalAlignment: 1
  m_VerticalAlignment: 1024
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: -50
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 0
  m_wordWrappingRatios: 0.4
  m_overflowMode: 1
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 15, y: 0, z: 15, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!114 &2070740858637139579
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8177616747508084018}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cc74d835ac42845df8ea6b46c1f44f3b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _textId: CHALLENGES_FAILED_TITLE
  _breakLines: 0
  _secondaryTexts: []
  _resizeFont: 0
--- !u!114 &823836187904062331
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8177616747508084018}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 597fa4b5a640f4f18af64afd8cea1598, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _raycast: 1
  _maskable: 1
  _suppressFont: 0
  _suppressMaterial: 0
  _suppressColorAndGradient: 1
  _suppressFontSize: 1
  _suppressSpacing: 0
  _suppressAlignment: 1
  _suppressOverflow: 0
  _suppressMargins: 1
  SelectedPresetName: ModalTitleRegular_TextMeshProStylePreset
  SelectedLayoutPresetName: ModalTitleRegular_TextMeshProLayoutPreset
--- !u!1 &8371837698338226347
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8499338271717474507}
  - component: {fileID: 7981809800320547090}
  - component: {fileID: 4356263003391597890}
  - component: {fileID: 5186924165877352200}
  - component: {fileID: 2578981679114023995}
  m_Layer: 5
  m_Name: Suggested
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &8499338271717474507
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8371837698338226347}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8572746615739997797}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 510, y: 50}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &7981809800320547090
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8371837698338226347}
  m_CullTransparentMesh: 1
--- !u!114 &4356263003391597890
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8371837698338226347}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: Suggested
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: e9fcd18707649e84790f7d932975e8cd, type: 2}
  m_sharedMaterial: {fileID: 2100000, guid: 119947acf130578418347521e0f1c21a, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4284994303
  m_fontColor: {r: 1, g: 0.8235294, b: 0.40392157, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 2
  m_fontColorGradient:
    topLeft: {r: 0.98823535, g: 0.9725491, b: 0.82745105, a: 1}
    topRight: {r: 0.98823535, g: 0.9725491, b: 0.82745105, a: 1}
    bottomLeft: {r: 0.854902, g: 0.654902, b: 0.4156863, a: 1}
    bottomRight: {r: 0.854902, g: 0.654902, b: 0.4156863, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 30
  m_fontSizeBase: 80
  m_fontWeight: 400
  m_enableAutoSizing: 1
  m_fontSizeMin: 18
  m_fontSizeMax: 30
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 1024
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: -50
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 0
  m_wordWrappingRatios: 0.4
  m_overflowMode: 1
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 15, y: 0, z: 15, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!114 &5186924165877352200
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8371837698338226347}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cc74d835ac42845df8ea6b46c1f44f3b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _textId: CHALLENGES_SUGGESTED_TITLE
  _breakLines: 0
  _secondaryTexts: []
  _resizeFont: 0
--- !u!114 &2578981679114023995
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8371837698338226347}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 597fa4b5a640f4f18af64afd8cea1598, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _raycast: 1
  _maskable: 1
  _suppressFont: 0
  _suppressMaterial: 0
  _suppressColorAndGradient: 1
  _suppressFontSize: 1
  _suppressSpacing: 0
  _suppressAlignment: 1
  _suppressOverflow: 0
  _suppressMargins: 1
  SelectedPresetName: ModalTitleRegular_TextMeshProStylePreset
  SelectedLayoutPresetName: ModalTitleRegular_TextMeshProLayoutPreset
--- !u!1 &8425323059747666745
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6347327441932060334}
  - component: {fileID: 6600118284794777509}
  - component: {fileID: 1826885042308994487}
  - component: {fileID: 5473369134517570090}
  m_Layer: 5
  m_Name: SelectedBGBase
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &6347327441932060334
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8425323059747666745}
  m_LocalRotation: {x: -0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6095267835751828747}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: -1, y: 1.5}
  m_SizeDelta: {x: -2, y: -3}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &6600118284794777509
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8425323059747666745}
  m_CullTransparentMesh: 1
--- !u!114 &1826885042308994487
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8425323059747666745}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: 1135918bd1512474b9ffe64850dcbea7, type: 3}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1.1
--- !u!114 &5473369134517570090
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8425323059747666745}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 45facfdc6a639f041b007c036dc527b8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _gradientType: 1
  _blendMode: 2
  _offset: 0
  _effectGradient:
    serializedVersion: 2
    key0: {r: 0.25882354, g: 0.47058827, b: 0.76470596, a: 1}
    key1: {r: 0.27450982, g: 0.54901963, b: 0.9450981, a: 1}
    key2: {r: 0, g: 0, b: 0, a: 0}
    key3: {r: 0, g: 0, b: 0, a: 0}
    key4: {r: 0, g: 0, b: 0, a: 0}
    key5: {r: 0, g: 0, b: 0, a: 0}
    key6: {r: 0, g: 0, b: 0, a: 0}
    key7: {r: 0, g: 0, b: 0, a: 0}
    ctime0: 0
    ctime1: 65535
    ctime2: 0
    ctime3: 0
    ctime4: 0
    ctime5: 0
    ctime6: 0
    ctime7: 0
    atime0: 0
    atime1: 65535
    atime2: 0
    atime3: 0
    atime4: 0
    atime5: 0
    atime6: 0
    atime7: 0
    m_Mode: 0
    m_ColorSpace: 0
    m_NumColorKeys: 2
    m_NumAlphaKeys: 2
--- !u!1 &8504102208618198786
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 431583278720427933}
  - component: {fileID: 4677478100309190644}
  - component: {fileID: 3053015144551004672}
  m_Layer: 5
  m_Name: PatternOverlay
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &431583278720427933
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8504102208618198786}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1231443325927710792}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &4677478100309190644
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8504102208618198786}
  m_CullTransparentMesh: 1
--- !u!114 &3053015144551004672
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8504102208618198786}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 0.6117647}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: 8c882f007563ffe4887b777ef4a5eec7, type: 3}
  m_Type: 2
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1.3
--- !u!1 &8592886731957071906
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5709950868351999918}
  - component: {fileID: 2179097678281023694}
  m_Layer: 5
  m_Name: NewChallengesPanel
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &5709950868351999918
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8592886731957071906}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8418551130642013644}
  - {fileID: 7113921931529501645}
  m_Father: {fileID: 1231443325927710792}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &2179097678281023694
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8592886731957071906}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1e8b7a48b57a4008bfb38b1a8e1585ed, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _favoritesTitleGameObject: {fileID: 1704840424077620171}
  _teamTitleGameObject: {fileID: 6275706713034392456}
  _suggestedTitleGameObject: {fileID: 8371837698338226347}
  _playerItemPrefab: {fileID: 7348202260022018221, guid: ffeac756154c13c41a851d4f0b676140,
    type: 3}
  _playerItemsRoot: {fileID: 8572746615739997797}
--- !u!1 &8647109786541659987
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 614659172238753362}
  m_Layer: 5
  m_Name: VisualRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &614659172238753362
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8647109786541659987}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 1
  m_Children:
  - {fileID: 1150874204090936929}
  m_Father: {fileID: 3182571624525991704}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 32}
  m_SizeDelta: {x: 590, y: 1100}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &8985688629752642992
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4520299061184886203}
  - component: {fileID: 866650504270191495}
  - component: {fileID: 6849724269763359897}
  m_Layer: 5
  m_Name: BarBGOutline
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &4520299061184886203
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8985688629752642992}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6798695155388857269}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &866650504270191495
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8985688629752642992}
  m_CullTransparentMesh: 1
--- !u!114 &6849724269763359897
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8985688629752642992}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0, g: 0, b: 0, a: 0.3764706}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: 473ab620fdf19420c938e9e83b5c13bb, type: 3}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 7.48
--- !u!1 &9022105122744978973
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1076015760758255598}
  - component: {fileID: 8250520515982661190}
  - component: {fileID: 5700182555547276039}
  m_Layer: 0
  m_Name: Scrim
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1076015760758255598
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9022105122744978973}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3674206149143901974}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &8250520515982661190
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9022105122744978973}
  m_CullTransparentMesh: 1
--- !u!114 &5700182555547276039
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9022105122744978973}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0, g: 0, b: 0, a: 0}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!1 &9055978630373928171
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5929562591517350087}
  - component: {fileID: 6019110571210670978}
  m_Layer: 5
  m_Name: ActiveGamesPanel
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &5929562591517350087
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9055978630373928171}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6980048128946032832}
  - {fileID: 3089483213225846045}
  m_Father: {fileID: 1231443325927710792}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &6019110571210670978
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9055978630373928171}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 82deed07cf974ce9bcefeefbbd5272d2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _maxSuggestedPlayers: 5
  _activeChallengesButton: {fileID: 2543304480882451988}
  _favoritesButton: {fileID: 2587131785092098234}
  _activeChallengesHolders:
  - {fileID: 2599157000112180534}
  _favoritesHolders:
  - {fileID: 2699129290034222208}
  _activeChallengeTabItemPrefab: {fileID: 7348202260022018221, guid: 50e87ef2f64275341843d7c2f598fbb1,
    type: 3}
  _favoriteTabItemPrefab: {fileID: 7348202260022018221, guid: a7fb7fbb7489ebb4982a959ddd8e0993,
    type: 3}
  _playerItemsRoot: {fileID: 4975385916393566175}
  _favoritesTitleGameObject: {fileID: 3151317646671371955}
  _suggestedTitleGameObject: {fileID: 1154796394066750947}
  _completedTitleGameObject: {fileID: 5687882459558423258}
  _failedTitleGameObject: {fileID: 8177616747508084018}
  _yourTurnTitleGameObject: {fileID: 8131529661694877009}
  _theirTurnTitleGameObject: {fileID: 6943254477892184034}
  _expiredTitleGameObject: {fileID: 4141970975613589075}
  _emptyText: {fileID: 7695447736659610262}
--- !u!1 &9137563867119893264
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5262535005294521540}
  - component: {fileID: 1100242223492074042}
  - component: {fileID: 1846293521746215062}
  - component: {fileID: 2587131785092098234}
  - component: {fileID: 6819579122766685478}
  m_Layer: 5
  m_Name: FavoritesTabButton
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &5262535005294521540
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9137563867119893264}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 837379061992744721}
  - {fileID: 3426846936298758478}
  - {fileID: 6095267835751828747}
  - {fileID: 7779867105281871483}
  - {fileID: 127589320422069527}
  m_Father: {fileID: 1634529854230486656}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 392.5, y: -32}
  m_SizeDelta: {x: 255, y: 64}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &1100242223492074042
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9137563867119893264}
  m_CullTransparentMesh: 1
--- !u!114 &1846293521746215062
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9137563867119893264}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e7d5d60348944b12b67e2f192ff73588, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _button: {fileID: 2587131785092098234}
  _selectedHolder:
  - {fileID: 2699129290034222208}
  _deselectedHolder:
  - {fileID: 3560250662791634631}
--- !u!114 &2587131785092098234
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9137563867119893264}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4e29b1a8efbd4b44bb3f3716e73f07ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 0
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 5101481975317722452}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &6819579122766685478
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9137563867119893264}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c55beeb4814bb114e9217839046395f3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _soundIdToPlayOnClick: GenericButtonTap
  _haptic: 1
  _impactPreset: 5
--- !u!1 &9189810267424671631
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1150874204090936929}
  m_Layer: 5
  m_Name: ModalHolder
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1150874204090936929
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9189810267424671631}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.9, y: 0.9, z: 0.9}
  m_ConstrainProportionsScale: 1
  m_Children:
  - {fileID: 3485055530798045257}
  - {fileID: 1231443325927710792}
  - {fileID: 849465541694258808}
  - {fileID: 974832844705285260}
  m_Father: {fileID: 614659172238753362}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &9194655991776382665
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2653116513856503030}
  - component: {fileID: 3232840302353273268}
  m_Layer: 5
  m_Name: ProgressLines
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2653116513856503030
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9194655991776382665}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 14243442040091414}
  m_Father: {fileID: 6798695155388857269}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &3232840302353273268
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9194655991776382665}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 30649d3a9faa99c48a7b1166b86bf2a0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Padding:
    m_Left: 0
    m_Right: 0
    m_Top: 0
    m_Bottom: 0
  m_ChildAlignment: 3
  m_Spacing: 0
  m_ChildForceExpandWidth: 1
  m_ChildForceExpandHeight: 1
  m_ChildControlWidth: 0
  m_ChildControlHeight: 1
  m_ChildScaleWidth: 0
  m_ChildScaleHeight: 0
  m_ReverseArrangement: 0
--- !u!1001 &359181580658186277
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 3182571624525991704}
    m_Modifications:
    - target: {fileID: 4552989811333622819, guid: f93e4ddbf6ac622419467bffaa4ed6a6,
        type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 4552989811333622819, guid: f93e4ddbf6ac622419467bffaa4ed6a6,
        type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 4552989811333622819, guid: f93e4ddbf6ac622419467bffaa4ed6a6,
        type: 3}
      propertyPath: m_RootOrder
      value: 7
      objectReference: {fileID: 0}
    - target: {fileID: 4552989811333622819, guid: f93e4ddbf6ac622419467bffaa4ed6a6,
        type: 3}
      propertyPath: m_AnchorMax.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 4552989811333622819, guid: f93e4ddbf6ac622419467bffaa4ed6a6,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 4552989811333622819, guid: f93e4ddbf6ac622419467bffaa4ed6a6,
        type: 3}
      propertyPath: m_AnchorMin.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 4552989811333622819, guid: f93e4ddbf6ac622419467bffaa4ed6a6,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 4552989811333622819, guid: f93e4ddbf6ac622419467bffaa4ed6a6,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 80
      objectReference: {fileID: 0}
    - target: {fileID: 4552989811333622819, guid: f93e4ddbf6ac622419467bffaa4ed6a6,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 80
      objectReference: {fileID: 0}
    - target: {fileID: 4552989811333622819, guid: f93e4ddbf6ac622419467bffaa4ed6a6,
        type: 3}
      propertyPath: m_LocalScale.x
      value: 0.9
      objectReference: {fileID: 0}
    - target: {fileID: 4552989811333622819, guid: f93e4ddbf6ac622419467bffaa4ed6a6,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 0.9
      objectReference: {fileID: 0}
    - target: {fileID: 4552989811333622819, guid: f93e4ddbf6ac622419467bffaa4ed6a6,
        type: 3}
      propertyPath: m_LocalScale.z
      value: 0.9
      objectReference: {fileID: 0}
    - target: {fileID: 4552989811333622819, guid: f93e4ddbf6ac622419467bffaa4ed6a6,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4552989811333622819, guid: f93e4ddbf6ac622419467bffaa4ed6a6,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4552989811333622819, guid: f93e4ddbf6ac622419467bffaa4ed6a6,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4552989811333622819, guid: f93e4ddbf6ac622419467bffaa4ed6a6,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4552989811333622819, guid: f93e4ddbf6ac622419467bffaa4ed6a6,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4552989811333622819, guid: f93e4ddbf6ac622419467bffaa4ed6a6,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4552989811333622819, guid: f93e4ddbf6ac622419467bffaa4ed6a6,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4552989811333622819, guid: f93e4ddbf6ac622419467bffaa4ed6a6,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4552989811333622819, guid: f93e4ddbf6ac622419467bffaa4ed6a6,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -528.0025
      objectReference: {fileID: 0}
    - target: {fileID: 4552989811333622819, guid: f93e4ddbf6ac622419467bffaa4ed6a6,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4552989811333622819, guid: f93e4ddbf6ac622419467bffaa4ed6a6,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4552989811333622819, guid: f93e4ddbf6ac622419467bffaa4ed6a6,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4552989811333622819, guid: f93e4ddbf6ac622419467bffaa4ed6a6,
        type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5605166543072783154, guid: f93e4ddbf6ac622419467bffaa4ed6a6,
        type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5654647216040760474, guid: f93e4ddbf6ac622419467bffaa4ed6a6,
        type: 3}
      propertyPath: m_Name
      value: CloseButtonRoot
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: f93e4ddbf6ac622419467bffaa4ed6a6, type: 3}
--- !u!224 &4310905416293676038 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 4552989811333622819, guid: f93e4ddbf6ac622419467bffaa4ed6a6,
    type: 3}
  m_PrefabInstance: {fileID: 359181580658186277}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &5167659423323288036 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 4849012730681420225, guid: f93e4ddbf6ac622419467bffaa4ed6a6,
    type: 3}
  m_PrefabInstance: {fileID: 359181580658186277}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4e29b1a8efbd4b44bb3f3716e73f07ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1001 &616802094659881285
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 6737153281501167778}
    m_Modifications:
    - target: {fileID: 394186021921663858, guid: 425d8d17c10558043b80a239ab8a3842,
        type: 3}
      propertyPath: m_Name
      value: ScrimRoot
      objectReference: {fileID: 0}
    - target: {fileID: 499960932438597770, guid: 425d8d17c10558043b80a239ab8a3842,
        type: 3}
      propertyPath: _delegateButton
      value: 
      objectReference: {fileID: 5167659423323288036}
    - target: {fileID: 1137128861016859123, guid: 425d8d17c10558043b80a239ab8a3842,
        type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 1137128861016859123, guid: 425d8d17c10558043b80a239ab8a3842,
        type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 1137128861016859123, guid: 425d8d17c10558043b80a239ab8a3842,
        type: 3}
      propertyPath: m_AnchorMax.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1137128861016859123, guid: 425d8d17c10558043b80a239ab8a3842,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1137128861016859123, guid: 425d8d17c10558043b80a239ab8a3842,
        type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1137128861016859123, guid: 425d8d17c10558043b80a239ab8a3842,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1137128861016859123, guid: 425d8d17c10558043b80a239ab8a3842,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1137128861016859123, guid: 425d8d17c10558043b80a239ab8a3842,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1137128861016859123, guid: 425d8d17c10558043b80a239ab8a3842,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1137128861016859123, guid: 425d8d17c10558043b80a239ab8a3842,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1137128861016859123, guid: 425d8d17c10558043b80a239ab8a3842,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1137128861016859123, guid: 425d8d17c10558043b80a239ab8a3842,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1137128861016859123, guid: 425d8d17c10558043b80a239ab8a3842,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1137128861016859123, guid: 425d8d17c10558043b80a239ab8a3842,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1137128861016859123, guid: 425d8d17c10558043b80a239ab8a3842,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1137128861016859123, guid: 425d8d17c10558043b80a239ab8a3842,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1137128861016859123, guid: 425d8d17c10558043b80a239ab8a3842,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1137128861016859123, guid: 425d8d17c10558043b80a239ab8a3842,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1137128861016859123, guid: 425d8d17c10558043b80a239ab8a3842,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1137128861016859123, guid: 425d8d17c10558043b80a239ab8a3842,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 425d8d17c10558043b80a239ab8a3842, type: 3}
--- !u!224 &524870086518532278 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 1137128861016859123, guid: 425d8d17c10558043b80a239ab8a3842,
    type: 3}
  m_PrefabInstance: {fileID: 616802094659881285}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &1327120342146917957
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 6099812445402726806}
    m_Modifications:
    - target: {fileID: 3499796416640876634, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_Maskable
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3499796416640876634, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_fontSizeMax
      value: 35
      objectReference: {fileID: 0}
    - target: {fileID: 3499796416640876634, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_fontSizeMin
      value: 21
      objectReference: {fileID: 0}
    - target: {fileID: 3499796416640876634, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_overflowMode
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3499796416640876634, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_VerticalAlignment
      value: 512
      objectReference: {fileID: 0}
    - target: {fileID: 3499796416640876634, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_enableVertexGradient
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3534391727040453526, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_Name
      value: GenericNotifier
      objectReference: {fileID: 0}
    - target: {fileID: 3534391727040453526, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3608858794550754034, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 3608858794550754034, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 3608858794550754034, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_RootOrder
      value: 6
      objectReference: {fileID: 0}
    - target: {fileID: 3608858794550754034, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_AnchorMax.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3608858794550754034, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3608858794550754034, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_AnchorMin.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3608858794550754034, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3608858794550754034, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 35
      objectReference: {fileID: 0}
    - target: {fileID: 3608858794550754034, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 35
      objectReference: {fileID: 0}
    - target: {fileID: 3608858794550754034, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3608858794550754034, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3608858794550754034, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3608858794550754034, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3608858794550754034, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 3608858794550754034, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 3608858794550754034, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 3608858794550754034, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: -15
      objectReference: {fileID: 0}
    - target: {fileID: 3608858794550754034, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -5
      objectReference: {fileID: 0}
    - target: {fileID: 3608858794550754034, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3608858794550754034, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3608858794550754034, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6535236702741602958, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: _highlightObjectPrefab
      value: 
      objectReference: {fileID: 953883476494627278, guid: 9b5b0a11cc96123489e4c3715d07dad7,
        type: 3}
    - target: {fileID: 6535236702741602958, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: _highlightObjectToActive
      value: 
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 3534391727040453526, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 9036140125291861049}
    - targetCorrespondingSourceObject: {fileID: 3534688782495406492, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 8256805957056645983}
  m_SourcePrefab: {fileID: 100100000, guid: 7d25b0479d4dcca498265377e94b5806, type: 3}
--- !u!224 &2341833428726537399 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 3608858794550754034, guid: 7d25b0479d4dcca498265377e94b5806,
    type: 3}
  m_PrefabInstance: {fileID: 1327120342146917957}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &2550812446672551379 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 3534391727040453526, guid: 7d25b0479d4dcca498265377e94b5806,
    type: 3}
  m_PrefabInstance: {fileID: 1327120342146917957}
  m_PrefabAsset: {fileID: 0}
--- !u!223 &9036140125291861049
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2550812446672551379}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 2
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 1
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_VertexColorAlwaysGammaSpace: 0
  m_AdditionalShaderChannelsFlag: 25
  m_UpdateRectTransformForStandalone: 0
  m_SortingLayerID: 0
  m_SortingOrder: 111
  m_TargetDisplay: 0
--- !u!1 &2551109226284908505 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 3534688782495406492, guid: 7d25b0479d4dcca498265377e94b5806,
    type: 3}
  m_PrefabInstance: {fileID: 1327120342146917957}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &8256805957056645983
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2551109226284908505}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 597fa4b5a640f4f18af64afd8cea1598, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _raycast: 0
  _maskable: 1
  _suppressFont: 0
  _suppressMaterial: 0
  _suppressColorAndGradient: 1
  _suppressFontSize: 1
  _suppressSpacing: 0
  _suppressAlignment: 1
  _suppressOverflow: 1
  _suppressMargins: 0
  SelectedPresetName: FailedRegularNumbers3_TextMeshProStylePreset
  SelectedLayoutPresetName: FailedRegularNumbers3_TextMeshProLayoutPreset
--- !u!1001 &2624536343584000807
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1150874204090936929}
    m_Modifications:
    - target: {fileID: 822629807720156859, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 822629807720156859, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 822629807720156859, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 822629807720156859, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 822629807720156859, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 822629807720156859, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1122135758442925273, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_text
      value: Trivia With Friends
      objectReference: {fileID: 0}
    - target: {fileID: 1122135758442925273, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_Maskable
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1122135758442925273, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_fontSize
      value: 38
      objectReference: {fileID: 0}
    - target: {fileID: 1122135758442925273, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_fontSizeMax
      value: 38
      objectReference: {fileID: 0}
    - target: {fileID: 1122135758442925273, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_fontSizeMin
      value: 18
      objectReference: {fileID: 0}
    - target: {fileID: 1122135758442925273, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_RaycastTarget
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1122135758442925273, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_VerticalAlignment
      value: 4096
      objectReference: {fileID: 0}
    - target: {fileID: 1122135758442925273, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_fontColorGradient.topLeft.b
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1122135758442925273, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_fontColorGradient.topLeft.g
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1122135758442925273, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_fontColorGradient.topLeft.r
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1122135758442925273, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_fontColorGradient.topRight.b
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1122135758442925273, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_fontColorGradient.topRight.g
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1122135758442925273, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_fontColorGradient.topRight.r
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1122135758442925273, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_fontColorGradient.bottomLeft.b
      value: 0.4745098
      objectReference: {fileID: 0}
    - target: {fileID: 1122135758442925273, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_fontColorGradient.bottomLeft.g
      value: 0.66856897
      objectReference: {fileID: 0}
    - target: {fileID: 1122135758442925273, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_fontColorGradient.bottomLeft.r
      value: 0.8862745
      objectReference: {fileID: 0}
    - target: {fileID: 1122135758442925273, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_fontColorGradient.bottomRight.b
      value: 0.4745098
      objectReference: {fileID: 0}
    - target: {fileID: 1122135758442925273, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_fontColorGradient.bottomRight.g
      value: 0.66856897
      objectReference: {fileID: 0}
    - target: {fileID: 1122135758442925273, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_fontColorGradient.bottomRight.r
      value: 0.8862745
      objectReference: {fileID: 0}
    - target: {fileID: 1122135758442925273, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: 'm_ActiveFontFeatures.Array.data[0]'
      value: 1801810542
      objectReference: {fileID: 0}
    - target: {fileID: 2912665100523563268, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: _textId
      value: CHALLENGE_TRIVIA_INFO_TITLE
      objectReference: {fileID: 0}
    - target: {fileID: 3429902773653075637, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3433380703032816991, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 3433380703032816991, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_Pivot.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3433380703032816991, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3433380703032816991, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_AnchorMax.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3433380703032816991, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3433380703032816991, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3433380703032816991, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3433380703032816991, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: -44.000008
      objectReference: {fileID: 0}
    - target: {fileID: 3433380703032816991, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 303.42
      objectReference: {fileID: 0}
    - target: {fileID: 3433380703032816991, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3433380703032816991, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3433380703032816991, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3433380703032816991, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3433380703032816991, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 3433380703032816991, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 3433380703032816991, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 3433380703032816991, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3433380703032816991, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3433380703032816991, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3433380703032816991, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3433380703032816991, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3984711081364375301, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3984711081364375301, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3984711081364375301, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: -140
      objectReference: {fileID: 0}
    - target: {fileID: 3984711081364375301, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 80
      objectReference: {fileID: 0}
    - target: {fileID: 3984711081364375301, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3984711081364375301, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -66
      objectReference: {fileID: 0}
    - target: {fileID: 4859645307979488393, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_Pivot.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4859645307979488393, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4859645307979488393, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 3.5
      objectReference: {fileID: 0}
    - target: {fileID: 4859645307979488393, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6003810636592720831, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6797266078053922215, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7279971381758438569, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_Color.b
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7279971381758438569, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_Color.g
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7279971381758438569, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_Color.r
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9114717711049487640, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_Name
      value: HeaderRoot
      objectReference: {fileID: 0}
    - target: {fileID: 9114717711049487640, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects:
    - {fileID: 6003810636592720831, guid: 7147cc4c9f7f02d4e846a864d60bd1b2, type: 3}
    - {fileID: 5567744163695063328, guid: 7147cc4c9f7f02d4e846a864d60bd1b2, type: 3}
    m_AddedGameObjects:
    - targetCorrespondingSourceObject: {fileID: 3433380703032816991, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      insertIndex: 0
      addedObject: {fileID: 9101073409767901634}
    - targetCorrespondingSourceObject: {fileID: 3433380703032816991, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      insertIndex: 1
      addedObject: {fileID: 8482133881373267648}
    - targetCorrespondingSourceObject: {fileID: 3433380703032816991, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      insertIndex: 2
      addedObject: {fileID: 4296161333995025605}
    - targetCorrespondingSourceObject: {fileID: 3433380703032816991, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      insertIndex: 4
      addedObject: {fileID: 8571093954883246119}
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 6945677697583403519, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 6280387707311959180}
  m_SourcePrefab: {fileID: 100100000, guid: 7147cc4c9f7f02d4e846a864d60bd1b2, type: 3}
--- !u!224 &849465541694258808 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 3433380703032816991, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
    type: 3}
  m_PrefabInstance: {fileID: 2624536343584000807}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &4902230827290761944 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 6945677697583403519, guid: 7147cc4c9f7f02d4e846a864d60bd1b2,
    type: 3}
  m_PrefabInstance: {fileID: 2624536343584000807}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &6280387707311959180
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4902230827290761944}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 597fa4b5a640f4f18af64afd8cea1598, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _raycast: 1
  _maskable: 1
  _suppressFont: 0
  _suppressMaterial: 0
  _suppressColorAndGradient: 1
  _suppressFontSize: 1
  _suppressSpacing: 0
  _suppressAlignment: 0
  _suppressOverflow: 0
  _suppressMargins: 0
  SelectedPresetName: ModalTitleRegular_TextMeshProStylePreset
  SelectedLayoutPresetName: ModalTitleRegular_TextMeshProLayoutPreset
--- !u!1001 &2798708647000781997
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1150874204090936929}
    m_Modifications:
    - target: {fileID: 1624221828596755684, guid: 02cc864e1a2a62e48842ce67f18a95d7,
        type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 1624221828596755684, guid: 02cc864e1a2a62e48842ce67f18a95d7,
        type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 1624221828596755684, guid: 02cc864e1a2a62e48842ce67f18a95d7,
        type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1624221828596755684, guid: 02cc864e1a2a62e48842ce67f18a95d7,
        type: 3}
      propertyPath: m_AnchorMax.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1624221828596755684, guid: 02cc864e1a2a62e48842ce67f18a95d7,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1624221828596755684, guid: 02cc864e1a2a62e48842ce67f18a95d7,
        type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1624221828596755684, guid: 02cc864e1a2a62e48842ce67f18a95d7,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1624221828596755684, guid: 02cc864e1a2a62e48842ce67f18a95d7,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 6
      objectReference: {fileID: 0}
    - target: {fileID: 1624221828596755684, guid: 02cc864e1a2a62e48842ce67f18a95d7,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 7.999901
      objectReference: {fileID: 0}
    - target: {fileID: 1624221828596755684, guid: 02cc864e1a2a62e48842ce67f18a95d7,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1624221828596755684, guid: 02cc864e1a2a62e48842ce67f18a95d7,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1624221828596755684, guid: 02cc864e1a2a62e48842ce67f18a95d7,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1624221828596755684, guid: 02cc864e1a2a62e48842ce67f18a95d7,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1624221828596755684, guid: 02cc864e1a2a62e48842ce67f18a95d7,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1624221828596755684, guid: 02cc864e1a2a62e48842ce67f18a95d7,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1624221828596755684, guid: 02cc864e1a2a62e48842ce67f18a95d7,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1624221828596755684, guid: 02cc864e1a2a62e48842ce67f18a95d7,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1624221828596755684, guid: 02cc864e1a2a62e48842ce67f18a95d7,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -1.000061
      objectReference: {fileID: 0}
    - target: {fileID: 1624221828596755684, guid: 02cc864e1a2a62e48842ce67f18a95d7,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1624221828596755684, guid: 02cc864e1a2a62e48842ce67f18a95d7,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1624221828596755684, guid: 02cc864e1a2a62e48842ce67f18a95d7,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9222248749183614586, guid: 02cc864e1a2a62e48842ce67f18a95d7,
        type: 3}
      propertyPath: m_Name
      value: BackgroundRoot
      objectReference: {fileID: 0}
    - target: {fileID: 9222248749183614586, guid: 02cc864e1a2a62e48842ce67f18a95d7,
        type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 02cc864e1a2a62e48842ce67f18a95d7, type: 3}
--- !u!224 &3485055530798045257 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 1624221828596755684, guid: 02cc864e1a2a62e48842ce67f18a95d7,
    type: 3}
  m_PrefabInstance: {fileID: 2798708647000781997}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &3833115921806578248
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1150874204090936929}
    m_Modifications:
    - target: {fileID: 2882923859994167100, guid: 0a910a24a4791264fa42ff09ae103d0d,
        type: 3}
      propertyPath: m_PixelsPerUnitMultiplier
      value: 1.05
      objectReference: {fileID: 0}
    - target: {fileID: 3236864483886531100, guid: 0a910a24a4791264fa42ff09ae103d0d,
        type: 3}
      propertyPath: m_Name
      value: BorderRoot
      objectReference: {fileID: 0}
    - target: {fileID: 4086660222337554116, guid: 0a910a24a4791264fa42ff09ae103d0d,
        type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 4086660222337554116, guid: 0a910a24a4791264fa42ff09ae103d0d,
        type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 4086660222337554116, guid: 0a910a24a4791264fa42ff09ae103d0d,
        type: 3}
      propertyPath: m_RootOrder
      value: 6
      objectReference: {fileID: 0}
    - target: {fileID: 4086660222337554116, guid: 0a910a24a4791264fa42ff09ae103d0d,
        type: 3}
      propertyPath: m_AnchorMax.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4086660222337554116, guid: 0a910a24a4791264fa42ff09ae103d0d,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4086660222337554116, guid: 0a910a24a4791264fa42ff09ae103d0d,
        type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4086660222337554116, guid: 0a910a24a4791264fa42ff09ae103d0d,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4086660222337554116, guid: 0a910a24a4791264fa42ff09ae103d0d,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4086660222337554116, guid: 0a910a24a4791264fa42ff09ae103d0d,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4086660222337554116, guid: 0a910a24a4791264fa42ff09ae103d0d,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4086660222337554116, guid: 0a910a24a4791264fa42ff09ae103d0d,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4086660222337554116, guid: 0a910a24a4791264fa42ff09ae103d0d,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4086660222337554116, guid: 0a910a24a4791264fa42ff09ae103d0d,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4086660222337554116, guid: 0a910a24a4791264fa42ff09ae103d0d,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4086660222337554116, guid: 0a910a24a4791264fa42ff09ae103d0d,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4086660222337554116, guid: 0a910a24a4791264fa42ff09ae103d0d,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4086660222337554116, guid: 0a910a24a4791264fa42ff09ae103d0d,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4086660222337554116, guid: 0a910a24a4791264fa42ff09ae103d0d,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4086660222337554116, guid: 0a910a24a4791264fa42ff09ae103d0d,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4086660222337554116, guid: 0a910a24a4791264fa42ff09ae103d0d,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4086660222337554116, guid: 0a910a24a4791264fa42ff09ae103d0d,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 0a910a24a4791264fa42ff09ae103d0d, type: 3}
--- !u!224 &974832844705285260 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 4086660222337554116, guid: 0a910a24a4791264fa42ff09ae103d0d,
    type: 3}
  m_PrefabInstance: {fileID: 3833115921806578248}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &6476913050089649289
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 5262535005294521540}
    m_Modifications:
    - target: {fileID: 3499796416640876634, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_Maskable
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3499796416640876634, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_fontSizeMax
      value: 35
      objectReference: {fileID: 0}
    - target: {fileID: 3499796416640876634, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_fontSizeMin
      value: 21
      objectReference: {fileID: 0}
    - target: {fileID: 3499796416640876634, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_overflowMode
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3499796416640876634, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_VerticalAlignment
      value: 512
      objectReference: {fileID: 0}
    - target: {fileID: 3499796416640876634, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_enableVertexGradient
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3534391727040453526, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_Name
      value: GenericNotifier
      objectReference: {fileID: 0}
    - target: {fileID: 3534391727040453526, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3608858794550754034, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 3608858794550754034, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 3608858794550754034, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_RootOrder
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 3608858794550754034, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_AnchorMax.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3608858794550754034, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3608858794550754034, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_AnchorMin.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3608858794550754034, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3608858794550754034, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 35
      objectReference: {fileID: 0}
    - target: {fileID: 3608858794550754034, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 35
      objectReference: {fileID: 0}
    - target: {fileID: 3608858794550754034, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3608858794550754034, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3608858794550754034, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3608858794550754034, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3608858794550754034, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 3608858794550754034, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 3608858794550754034, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 3608858794550754034, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: -15
      objectReference: {fileID: 0}
    - target: {fileID: 3608858794550754034, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -5
      objectReference: {fileID: 0}
    - target: {fileID: 3608858794550754034, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3608858794550754034, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3608858794550754034, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6535236702741602958, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      propertyPath: _highlightObjectPrefab
      value: 
      objectReference: {fileID: 953883476494627278, guid: 9b5b0a11cc96123489e4c3715d07dad7,
        type: 3}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 3534391727040453526, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 4244321690089768344}
    - targetCorrespondingSourceObject: {fileID: 3534688782495406492, guid: 7d25b0479d4dcca498265377e94b5806,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 1469919292459507607}
  m_SourcePrefab: {fileID: 100100000, guid: 7d25b0479d4dcca498265377e94b5806, type: 3}
--- !u!1 &7561034249180819231 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 3534391727040453526, guid: 7d25b0479d4dcca498265377e94b5806,
    type: 3}
  m_PrefabInstance: {fileID: 6476913050089649289}
  m_PrefabAsset: {fileID: 0}
--- !u!223 &4244321690089768344
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7561034249180819231}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 2
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 1
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_VertexColorAlwaysGammaSpace: 0
  m_AdditionalShaderChannelsFlag: 25
  m_UpdateRectTransformForStandalone: 0
  m_SortingLayerID: 0
  m_SortingOrder: 111
  m_TargetDisplay: 0
--- !u!1 &7561300378820357397 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 3534688782495406492, guid: 7d25b0479d4dcca498265377e94b5806,
    type: 3}
  m_PrefabInstance: {fileID: 6476913050089649289}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &1469919292459507607
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7561300378820357397}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 597fa4b5a640f4f18af64afd8cea1598, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _raycast: 0
  _maskable: 1
  _suppressFont: 0
  _suppressMaterial: 0
  _suppressColorAndGradient: 1
  _suppressFontSize: 1
  _suppressSpacing: 0
  _suppressAlignment: 1
  _suppressOverflow: 0
  _suppressMargins: 0
  SelectedPresetName: FailedRegularNumbers3_TextMeshProStylePreset
  SelectedLayoutPresetName: FailedRegularNumbers3_TextMeshProLayoutPreset
--- !u!224 &7779867105281871483 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 3608858794550754034, guid: 7d25b0479d4dcca498265377e94b5806,
    type: 3}
  m_PrefabInstance: {fileID: 6476913050089649289}
  m_PrefabAsset: {fileID: 0}
