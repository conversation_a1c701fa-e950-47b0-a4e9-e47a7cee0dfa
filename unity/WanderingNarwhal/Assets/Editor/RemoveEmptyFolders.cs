using System;
using UnityEngine;
using UnityEditor;
using System.IO;
using System.Collections.Generic;
using System.Linq;

public class RemoveEmptyFolders : Editor
{
    [MenuItem("BebopBee/Cleanup/Clean Empty Folders")]
    public static void CleanEmptyFolders()
    {
        var assetsPath = Application.dataPath;
        var allDirs = Directory.GetDirectories(assetsPath, "*", SearchOption.AllDirectories);
        var dirList = new List<string>(allDirs);

        dirList.Sort((a, b) =>
            b.Split(Path.DirectorySeparatorChar).Length.CompareTo(a.Split(Path.DirectorySeparatorChar).Length));

        var removedCount = 0;
        foreach (var relativePath in from dir in dirList
                 where IsFolderEmpty(dir)
                 select Path.Combine("Assets",
                     dir[assetsPath.Length..].Trim(Path.DirectorySeparatorChar, Path.AltDirectorySeparatorChar)))
        {
            Debug.Log($"Deleting empty folder: {relativePath}");
            if (AssetDatabase.DeleteAsset(relativePath))
            {
                removedCount++;
            }
            else
            {
                Debug.LogWarning($"Failed to delete: {relativePath}");
            }
        }

        AssetDatabase.Refresh();
        Debug.Log($"Total empty folders deleted: {removedCount}");
    }

    private static bool IsFolderEmpty(string folderPath)
    {
        return Directory.GetFiles(folderPath, "*", SearchOption.TopDirectoryOnly)
            .All(file => file.EndsWith(".meta", StringComparison.OrdinalIgnoreCase)) && Directory
            .GetDirectories(folderPath, "*", SearchOption.TopDirectoryOnly).All(IsFolderEmpty);
    }
}